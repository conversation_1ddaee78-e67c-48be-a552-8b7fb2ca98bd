"""
电力现货市场预测 ML 流水线 - 模型评估模块
作者：Gregor
版本：v0.1-MVP
日期：2025-08-25

功能：
1. 综合模型性能评估
2. 多维度评估指标计算
3. 模型比较和排序
4. 可视化评估结果
5. 生成评估报告
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import yaml
import logging
import joblib
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from sklearn.metrics import mean_absolute_error, mean_squared_error, mean_absolute_percentage_error, r2_score
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


class PowerMarketModelEvaluator:
    """电力市场模型评估器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """初始化模型评估器"""
        self.config = self._load_config(config_path)
        self.model_data_path = Path(self.config['data']['storage']['model_data_path'])
        self.feature_store_path = Path(self.config['data']['storage']['feature_store_path'])
        
        # 创建评估结果目录
        self.evaluation_path = Path("evaluation_results")
        self.evaluation_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化评估器组件
        self.metrics_calculator = MetricsCalculator(self.config)
        self.visualization_generator = VisualizationGenerator(self.config)
        self.report_generator = ReportGenerator(self.config)
        
        logger.info("模型评估器初始化完成")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def load_models_and_data(self) -> Tuple[Dict, Dict]:
        """加载模型和测试数据"""
        # 加载模型
        models = {}
        model_files = list(self.model_data_path.glob("*_model.*"))
        
        for model_file in model_files:
            model_name = model_file.stem.replace('_model', '')
            try:
                if model_file.suffix == '.joblib':
                    model = joblib.load(model_file)
                    models[model_name] = model
                    logger.info(f"加载模型: {model_name}")
                elif model_file.suffix == '.pth':
                    # PyTorch模型需要重新构建架构
                    logger.warning(f"跳过PyTorch模型: {model_name} (需要模型架构)")
            except Exception as e:
                logger.error(f"加载模型 {model_name} 失败: {str(e)}")
        
        # 加载测试数据
        test_data = {}
        test_file = self.feature_store_path / "test_features.parquet"
        if test_file.exists():
            df = pd.read_parquet(test_file)
            
            # 分离特征和目标
            target_column = 'price'
            if target_column in df.columns:
                feature_columns = [col for col in df.columns if col != target_column]
                test_data = {
                    'X': df[feature_columns].fillna(0).values,
                    'y': df[target_column].fillna(0).values,
                    'feature_names': feature_columns,
                    'timestamps': df.index,
                    'df': df
                }
                logger.info(f"加载测试数据: {len(df)} 条记录, {len(feature_columns)} 个特征")
            else:
                logger.error(f"测试数据中未找到目标列: {target_column}")
        else:
            logger.error(f"测试数据文件不存在: {test_file}")
        
        return models, test_data
    
    def generate_predictions(self, models: Dict, test_data: Dict) -> Dict[str, np.ndarray]:
        """生成所有模型的预测结果"""
        logger.info("生成模型预测结果")
        
        predictions = {}
        X_test = test_data['X']
        
        for model_name, model in models.items():
            try:
                if hasattr(model, 'predict'):
                    y_pred = model.predict(X_test)
                    predictions[model_name] = y_pred
                    logger.info(f"生成 {model_name} 预测结果")
                else:
                    logger.warning(f"模型 {model_name} 没有predict方法")
            except Exception as e:
                logger.error(f"生成 {model_name} 预测失败: {str(e)}")
        
        return predictions
    
    def evaluate_all_models(self, predictions: Dict[str, np.ndarray], test_data: Dict) -> Dict:
        """评估所有模型"""
        logger.info("开始综合模型评估")
        
        y_true = test_data['y']
        timestamps = test_data['timestamps']
        
        evaluation_results = {}
        
        for model_name, y_pred in predictions.items():
            logger.info(f"评估模型: {model_name}")
            
            # 计算各类评估指标
            model_results = {
                'accuracy_metrics': self.metrics_calculator.calculate_accuracy_metrics(y_true, y_pred),
                'directional_metrics': self.metrics_calculator.calculate_directional_metrics(y_true, y_pred),
                'probabilistic_metrics': self.metrics_calculator.calculate_probabilistic_metrics(y_true, y_pred),
                'robustness_metrics': self.metrics_calculator.calculate_robustness_metrics(y_true, y_pred),
                'economic_metrics': self.metrics_calculator.calculate_economic_metrics(y_true, y_pred),
                'realtime_metrics': self.metrics_calculator.calculate_realtime_metrics(y_true, y_pred),
                'time_series_metrics': self.metrics_calculator.calculate_time_series_metrics(y_true, y_pred, timestamps)
            }
            
            evaluation_results[model_name] = model_results
        
        logger.info("模型评估完成")
        return evaluation_results
    
    def rank_models(self, evaluation_results: Dict) -> List[Tuple[str, float]]:
        """对模型进行排序"""
        logger.info("对模型进行综合排序")
        
        model_scores = {}
        
        for model_name, results in evaluation_results.items():
            # 综合评分（权重可调整）
            score = 0
            
            # 准确性指标 (40%)
            accuracy = results['accuracy_metrics']
            mape_score = max(0, 1 - accuracy.get('mape', 1))  # MAPE越小越好
            rmse_score = max(0, 1 - accuracy.get('rmse_normalized', 1))
            score += 0.4 * (mape_score + rmse_score) / 2
            
            # 方向性指标 (20%)
            directional = results['directional_metrics']
            direction_score = directional.get('direction_accuracy', 0)
            score += 0.2 * direction_score
            
            # 鲁棒性指标 (20%)
            robustness = results['robustness_metrics']
            robustness_score = robustness.get('stability_score', 0)
            score += 0.2 * robustness_score
            
            # 经济性指标 (20%)
            economic = results['economic_metrics']
            economic_score = max(0, economic.get('profit_score', 0))
            score += 0.2 * economic_score
            
            model_scores[model_name] = score
        
        # 按分数排序
        ranked_models = sorted(model_scores.items(), key=lambda x: x[1], reverse=True)
        
        logger.info("模型排序完成:")
        for i, (model_name, score) in enumerate(ranked_models, 1):
            logger.info(f"  {i}. {model_name}: {score:.4f}")
        
        return ranked_models
    
    def generate_visualizations(self, predictions: Dict, test_data: Dict, evaluation_results: Dict):
        """生成可视化图表"""
        logger.info("生成可视化图表")
        
        y_true = test_data['y']
        timestamps = test_data['timestamps']
        
        # 1. 预测vs实际值对比图
        self.visualization_generator.plot_predictions_vs_actual(
            predictions, y_true, timestamps, 
            save_path=self.evaluation_path / "predictions_vs_actual.html"
        )
        
        # 2. 误差分布图
        self.visualization_generator.plot_error_distribution(
            predictions, y_true,
            save_path=self.evaluation_path / "error_distribution.html"
        )
        
        # 3. 模型性能雷达图
        self.visualization_generator.plot_model_performance_radar(
            evaluation_results,
            save_path=self.evaluation_path / "performance_radar.html"
        )
        
        # 4. 时间序列预测图
        self.visualization_generator.plot_time_series_predictions(
            predictions, y_true, timestamps,
            save_path=self.evaluation_path / "time_series_predictions.html"
        )
        
        # 5. 评估指标对比图
        self.visualization_generator.plot_metrics_comparison(
            evaluation_results,
            save_path=self.evaluation_path / "metrics_comparison.html"
        )
        
        logger.info("可视化图表生成完成")
    
    def generate_evaluation_report(self, evaluation_results: Dict, ranked_models: List, predictions: Dict, test_data: Dict):
        """生成评估报告"""
        logger.info("生成评估报告")
        
        report = self.report_generator.generate_comprehensive_report(
            evaluation_results, ranked_models, predictions, test_data
        )
        
        # 保存报告
        report_path = self.evaluation_path / "evaluation_report.html"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"评估报告已保存: {report_path}")
    
    def run_evaluation_pipeline(self):
        """运行完整的模型评估流水线"""
        logger.info("开始运行模型评估流水线")
        
        try:
            # 1. 加载模型和数据
            models, test_data = self.load_models_and_data()
            if not models or not test_data:
                logger.error("无法加载模型或测试数据，评估流水线终止")
                return
            
            # 2. 生成预测结果
            predictions = self.generate_predictions(models, test_data)
            if not predictions:
                logger.error("无法生成预测结果，评估流水线终止")
                return
            
            # 3. 评估所有模型
            evaluation_results = self.evaluate_all_models(predictions, test_data)
            
            # 4. 模型排序
            ranked_models = self.rank_models(evaluation_results)
            
            # 5. 生成可视化图表
            self.generate_visualizations(predictions, test_data, evaluation_results)
            
            # 6. 生成评估报告
            self.generate_evaluation_report(evaluation_results, ranked_models, predictions, test_data)
            
            # 7. 保存评估结果
            results_path = self.evaluation_path / "complete_evaluation_results.joblib"
            joblib.dump({
                'evaluation_results': evaluation_results,
                'ranked_models': ranked_models,
                'predictions': predictions
            }, results_path)
            
            logger.info("模型评估流水线完成")
            
            # 返回评估摘要
            summary = {
                'evaluated_models': list(predictions.keys()),
                'best_model': ranked_models[0][0] if ranked_models else "unknown",
                'best_model_score': ranked_models[0][1] if ranked_models else 0,
                'test_samples': len(test_data['y']),
                'evaluation_metrics': list(evaluation_results[list(evaluation_results.keys())[0]].keys()) if evaluation_results else []
            }
            
            logger.info(f"评估摘要: {summary}")
            return summary
            
        except Exception as e:
            logger.error(f"模型评估流水线执行失败: {str(e)}")
            raise


class MetricsCalculator:
    """评估指标计算器"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def calculate_accuracy_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """计算准确性指标"""
        # 清理数据
        mask = ~(np.isnan(y_true) | np.isnan(y_pred))
        y_true_clean = y_true[mask]
        y_pred_clean = y_pred[mask]
        
        if len(y_true_clean) == 0:
            return {'mae': float('inf'), 'rmse': float('inf'), 'mape': float('inf'), 'r2': -float('inf')}
        
        metrics = {}
        metrics['mae'] = mean_absolute_error(y_true_clean, y_pred_clean)
        metrics['rmse'] = np.sqrt(mean_squared_error(y_true_clean, y_pred_clean))
        
        # MAPE
        mape_mask = y_true_clean != 0
        if np.sum(mape_mask) > 0:
            metrics['mape'] = np.mean(np.abs((y_true_clean[mape_mask] - y_pred_clean[mape_mask]) / y_true_clean[mape_mask]))
        else:
            metrics['mape'] = float('inf')
        
        # R²
        metrics['r2'] = r2_score(y_true_clean, y_pred_clean)
        
        # 标准化RMSE
        y_range = np.max(y_true_clean) - np.min(y_true_clean)
        metrics['rmse_normalized'] = metrics['rmse'] / (y_range + 1e-8)
        
        return metrics
    
    def calculate_directional_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """计算方向性指标"""
        if len(y_true) < 2:
            return {'direction_accuracy': 0.0, 'trend_consistency': 0.0}
        
        # 方向准确率
        true_direction = np.diff(y_true) > 0
        pred_direction = np.diff(y_pred) > 0
        direction_accuracy = np.mean(true_direction == pred_direction)
        
        # 趋势一致性（连续3个点的趋势）
        trend_consistency = 0.0
        if len(y_true) >= 4:
            true_trends = []
            pred_trends = []
            
            for i in range(len(y_true) - 3):
                true_trend = np.polyfit(range(4), y_true[i:i+4], 1)[0]
                pred_trend = np.polyfit(range(4), y_pred[i:i+4], 1)[0]
                true_trends.append(true_trend > 0)
                pred_trends.append(pred_trend > 0)
            
            trend_consistency = np.mean(np.array(true_trends) == np.array(pred_trends))
        
        return {
            'direction_accuracy': direction_accuracy,
            'trend_consistency': trend_consistency
        }
    
    def calculate_probabilistic_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """计算概率性指标"""
        # 简化的概率指标
        residuals = y_true - y_pred
        
        # 预测区间覆盖率（假设正态分布）
        std_residuals = np.std(residuals)
        coverage_80 = np.mean(np.abs(residuals) <= 1.28 * std_residuals)  # 80%置信区间
        coverage_95 = np.mean(np.abs(residuals) <= 1.96 * std_residuals)  # 95%置信区间
        
        return {
            'coverage_80': coverage_80,
            'coverage_95': coverage_95,
            'residual_std': std_residuals
        }
    
    def calculate_robustness_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """计算鲁棒性指标"""
        residuals = y_true - y_pred
        
        # 极端误差比例
        mae = np.mean(np.abs(residuals))
        extreme_error_ratio = np.mean(np.abs(residuals) > 3 * mae)
        
        # 稳定性评分
        rolling_mae = []
        window_size = min(100, len(residuals) // 10)
        
        for i in range(len(residuals) - window_size + 1):
            window_mae = np.mean(np.abs(residuals[i:i+window_size]))
            rolling_mae.append(window_mae)
        
        stability_score = 1 - (np.std(rolling_mae) / (np.mean(rolling_mae) + 1e-8))
        
        return {
            'extreme_error_ratio': extreme_error_ratio,
            'stability_score': max(0, stability_score)
        }
    
    def calculate_economic_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """计算经济性指标"""
        # 简化的经济指标
        
        # 预测价值（基于方向预测的收益）
        if len(y_true) < 2:
            return {'profit_score': 0.0, 'sharpe_ratio': 0.0}
        
        price_changes = np.diff(y_true)
        predicted_changes = np.diff(y_pred)
        
        # 简单交易策略：预测上涨时买入，预测下跌时卖出
        trading_signals = np.sign(predicted_changes)
        returns = trading_signals * price_changes
        
        # 累计收益
        cumulative_return = np.sum(returns)
        
        # 夏普比率
        if np.std(returns) > 0:
            sharpe_ratio = np.mean(returns) / np.std(returns)
        else:
            sharpe_ratio = 0.0
        
        # 利润评分（标准化）
        profit_score = max(0, min(1, (cumulative_return / (np.sum(np.abs(price_changes)) + 1e-8) + 1) / 2))
        
        return {
            'profit_score': profit_score,
            'sharpe_ratio': sharpe_ratio,
            'cumulative_return': cumulative_return
        }
    
    def calculate_realtime_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """计算实时性指标"""
        # 模拟实时性指标
        return {
            'prediction_latency': 0.025,  # 假设25ms
            'system_response_time': 0.050  # 假设50ms
        }
    
    def calculate_time_series_metrics(self, y_true: np.ndarray, y_pred: np.ndarray, timestamps: pd.Index) -> Dict[str, float]:
        """计算时间序列特定指标"""
        if len(y_true) < 24:  # 至少需要24个点
            return {'seasonal_accuracy': 0.0, 'peak_detection_accuracy': 0.0}
        
        # 季节性准确率（简化）
        if len(y_true) >= 96:  # 至少一天的数据
            daily_pattern_true = []
            daily_pattern_pred = []
            
            for i in range(0, len(y_true) - 95, 96):
                daily_pattern_true.extend(y_true[i:i+96])
                daily_pattern_pred.extend(y_pred[i:i+96])
            
            seasonal_corr = np.corrcoef(daily_pattern_true, daily_pattern_pred)[0, 1]
            seasonal_accuracy = max(0, seasonal_corr)
        else:
            seasonal_accuracy = 0.0
        
        # 峰值检测准确率
        true_peaks = self._find_peaks(y_true)
        pred_peaks = self._find_peaks(y_pred)
        
        if len(true_peaks) > 0:
            peak_detection_accuracy = len(set(true_peaks) & set(pred_peaks)) / len(true_peaks)
        else:
            peak_detection_accuracy = 0.0
        
        return {
            'seasonal_accuracy': seasonal_accuracy,
            'peak_detection_accuracy': peak_detection_accuracy
        }
    
    def _find_peaks(self, data: np.ndarray, prominence: float = 0.1) -> List[int]:
        """简单的峰值检测"""
        peaks = []
        threshold = np.std(data) * prominence
        
        for i in range(1, len(data) - 1):
            if (data[i] > data[i-1] and data[i] > data[i+1] and 
                data[i] - min(data[i-1], data[i+1]) > threshold):
                peaks.append(i)
        
        return peaks


class VisualizationGenerator:
    """可视化生成器"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def plot_predictions_vs_actual(self, predictions: Dict, y_true: np.ndarray, timestamps: pd.Index, save_path: Path):
        """绘制预测vs实际值对比图"""
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=('时间序列预测对比', '散点图对比'),
            vertical_spacing=0.1
        )
        
        # 时间序列图
        fig.add_trace(
            go.Scatter(x=timestamps, y=y_true, name='实际值', line=dict(color='black', width=2)),
            row=1, col=1
        )
        
        colors = ['red', 'blue', 'green', 'orange', 'purple']
        for i, (model_name, y_pred) in enumerate(predictions.items()):
            color = colors[i % len(colors)]
            fig.add_trace(
                go.Scatter(x=timestamps, y=y_pred, name=f'{model_name}预测', 
                          line=dict(color=color, width=1, dash='dash')),
                row=1, col=1
            )
        
        # 散点图
        for i, (model_name, y_pred) in enumerate(predictions.items()):
            color = colors[i % len(colors)]
            fig.add_trace(
                go.Scatter(x=y_true, y=y_pred, mode='markers', name=f'{model_name}',
                          marker=dict(color=color, size=4, opacity=0.6)),
                row=2, col=1
            )
        
        # 添加对角线
        min_val, max_val = min(y_true.min(), min(pred.min() for pred in predictions.values())), \
                          max(y_true.max(), max(pred.max() for pred in predictions.values()))
        fig.add_trace(
            go.Scatter(x=[min_val, max_val], y=[min_val, max_val], 
                      mode='lines', name='理想预测线', line=dict(color='gray', dash='dot')),
            row=2, col=1
        )
        
        fig.update_layout(
            title='模型预测效果对比',
            height=800,
            showlegend=True
        )
        
        fig.write_html(save_path)
    
    def plot_error_distribution(self, predictions: Dict, y_true: np.ndarray, save_path: Path):
        """绘制误差分布图"""
        fig = make_subplots(
            rows=1, cols=2,
            subplot_titles=('误差分布直方图', '误差箱线图')
        )
        
        colors = ['red', 'blue', 'green', 'orange', 'purple']
        
        for i, (model_name, y_pred) in enumerate(predictions.items()):
            errors = y_true - y_pred
            color = colors[i % len(colors)]
            
            # 直方图
            fig.add_trace(
                go.Histogram(x=errors, name=f'{model_name}误差', 
                           marker_color=color, opacity=0.7, nbinsx=30),
                row=1, col=1
            )
            
            # 箱线图
            fig.add_trace(
                go.Box(y=errors, name=model_name, marker_color=color),
                row=1, col=2
            )
        
        fig.update_layout(
            title='模型预测误差分布',
            height=500,
            showlegend=True
        )
        
        fig.write_html(save_path)
    
    def plot_model_performance_radar(self, evaluation_results: Dict, save_path: Path):
        """绘制模型性能雷达图"""
        # 提取关键指标
        metrics_to_plot = ['mape', 'direction_accuracy', 'stability_score', 'profit_score']
        
        fig = go.Figure()
        
        colors = ['red', 'blue', 'green', 'orange', 'purple']
        
        for i, (model_name, results) in enumerate(evaluation_results.items()):
            values = []
            
            # MAPE (转换为准确率)
            mape = results['accuracy_metrics'].get('mape', 1)
            mape_score = max(0, 1 - min(mape, 1))
            values.append(mape_score)
            
            # 方向准确率
            direction_acc = results['directional_metrics'].get('direction_accuracy', 0)
            values.append(direction_acc)
            
            # 稳定性评分
            stability = results['robustness_metrics'].get('stability_score', 0)
            values.append(stability)
            
            # 利润评分
            profit = results['economic_metrics'].get('profit_score', 0)
            values.append(profit)
            
            # 闭合雷达图
            values.append(values[0])
            
            color = colors[i % len(colors)]
            fig.add_trace(go.Scatterpolar(
                r=values,
                theta=metrics_to_plot + [metrics_to_plot[0]],
                fill='toself',
                name=model_name,
                line_color=color
            ))
        
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 1]
                )),
            showlegend=True,
            title="模型性能雷达图"
        )
        
        fig.write_html(save_path)
    
    def plot_time_series_predictions(self, predictions: Dict, y_true: np.ndarray, timestamps: pd.Index, save_path: Path):
        """绘制时间序列预测详细图"""
        # 只显示最近的数据点以便观察
        n_points = min(500, len(y_true))
        recent_timestamps = timestamps[-n_points:]
        recent_y_true = y_true[-n_points:]
        
        fig = go.Figure()
        
        # 实际值
        fig.add_trace(go.Scatter(
            x=recent_timestamps, 
            y=recent_y_true, 
            name='实际值',
            line=dict(color='black', width=3)
        ))
        
        # 预测值
        colors = ['red', 'blue', 'green', 'orange', 'purple']
        for i, (model_name, y_pred) in enumerate(predictions.items()):
            recent_y_pred = y_pred[-n_points:]
            color = colors[i % len(colors)]
            
            fig.add_trace(go.Scatter(
                x=recent_timestamps,
                y=recent_y_pred,
                name=f'{model_name}预测',
                line=dict(color=color, width=2, dash='dash')
            ))
        
        fig.update_layout(
            title=f'时间序列预测详细图 (最近{n_points}个数据点)',
            xaxis_title='时间',
            yaxis_title='价格',
            height=600,
            showlegend=True
        )
        
        fig.write_html(save_path)
    
    def plot_metrics_comparison(self, evaluation_results: Dict, save_path: Path):
        """绘制评估指标对比图"""
        # 准备数据
        models = list(evaluation_results.keys())
        metrics_data = []
        
        for model_name, results in evaluation_results.items():
            row = {'Model': model_name}
            
            # 提取关键指标
            row['MAPE'] = results['accuracy_metrics'].get('mape', 0)
            row['RMSE'] = results['accuracy_metrics'].get('rmse', 0)
            row['R²'] = results['accuracy_metrics'].get('r2', 0)
            row['Direction Accuracy'] = results['directional_metrics'].get('direction_accuracy', 0)
            row['Stability Score'] = results['robustness_metrics'].get('stability_score', 0)
            row['Profit Score'] = results['economic_metrics'].get('profit_score', 0)
            
            metrics_data.append(row)
        
        df_metrics = pd.DataFrame(metrics_data)
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=3,
            subplot_titles=('MAPE', 'RMSE', 'R²', 'Direction Accuracy', 'Stability Score', 'Profit Score')
        )
        
        metrics = ['MAPE', 'RMSE', 'R²', 'Direction Accuracy', 'Stability Score', 'Profit Score']
        positions = [(1,1), (1,2), (1,3), (2,1), (2,2), (2,3)]
        
        for metric, (row, col) in zip(metrics, positions):
            fig.add_trace(
                go.Bar(x=df_metrics['Model'], y=df_metrics[metric], name=metric, showlegend=False),
                row=row, col=col
            )
        
        fig.update_layout(
            title='模型评估指标对比',
            height=800,
            showlegend=False
        )
        
        fig.write_html(save_path)


class ReportGenerator:
    """报告生成器"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def generate_comprehensive_report(self, evaluation_results: Dict, ranked_models: List, 
                                    predictions: Dict, test_data: Dict) -> str:
        """生成综合评估报告"""
        
        html_template = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>电力现货市场预测模型评估报告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
                .header {{ background-color: #f4f4f4; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; }}
                .model-rank {{ background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0; }}
                .metrics-table {{ width: 100%; border-collapse: collapse; margin: 15px 0; }}
                .metrics-table th, .metrics-table td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                .metrics-table th {{ background-color: #f2f2f2; }}
                .best-model {{ background-color: #d4edda; border: 1px solid #c3e6cb; }}
                .summary-box {{ background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>电力现货市场预测模型评估报告</h1>
                <p><strong>生成时间:</strong> {timestamp}</p>
                <p><strong>评估数据量:</strong> {test_samples} 个样本</p>
                <p><strong>评估模型数量:</strong> {model_count} 个</p>
            </div>
            
            <div class="section">
                <h2>1. 执行摘要</h2>
                <div class="summary-box">
                    <p><strong>最佳模型:</strong> {best_model}</p>
                    <p><strong>最佳模型综合评分:</strong> {best_score:.4f}</p>
                    <p><strong>评估维度:</strong> 准确性、方向性、鲁棒性、经济性、实时性</p>
                </div>
            </div>
            
            <div class="section">
                <h2>2. 模型排序</h2>
                {model_rankings}
            </div>
            
            <div class="section">
                <h2>3. 详细评估指标</h2>
                {detailed_metrics}
            </div>
            
            <div class="section">
                <h2>4. 关键发现</h2>
                {key_findings}
            </div>
            
            <div class="section">
                <h2>5. 建议</h2>
                {recommendations}
            </div>
        </body>
        </html>
        """
        
        # 生成各部分内容
        timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")
        test_samples = len(test_data['y'])
        model_count = len(evaluation_results)
        best_model = ranked_models[0][0] if ranked_models else "未知"
        best_score = ranked_models[0][1] if ranked_models else 0
        
        model_rankings = self._generate_model_rankings_html(ranked_models)
        detailed_metrics = self._generate_detailed_metrics_html(evaluation_results)
        key_findings = self._generate_key_findings_html(evaluation_results, ranked_models)
        recommendations = self._generate_recommendations_html(evaluation_results, ranked_models)
        
        # 填充模板
        report = html_template.format(
            timestamp=timestamp,
            test_samples=test_samples,
            model_count=model_count,
            best_model=best_model,
            best_score=best_score,
            model_rankings=model_rankings,
            detailed_metrics=detailed_metrics,
            key_findings=key_findings,
            recommendations=recommendations
        )
        
        return report
    
    def _generate_model_rankings_html(self, ranked_models: List) -> str:
        """生成模型排序HTML"""
        html = ""
        for i, (model_name, score) in enumerate(ranked_models, 1):
            class_name = "model-rank best-model" if i == 1 else "model-rank"
            html += f"""
            <div class="{class_name}">
                <h3>{i}. {model_name}</h3>
                <p><strong>综合评分:</strong> {score:.4f}</p>
            </div>
            """
        return html
    
    def _generate_detailed_metrics_html(self, evaluation_results: Dict) -> str:
        """生成详细指标HTML"""
        html = "<table class='metrics-table'>"
        html += "<tr><th>模型</th><th>MAPE</th><th>RMSE</th><th>R²</th><th>方向准确率</th><th>稳定性评分</th><th>利润评分</th></tr>"
        
        for model_name, results in evaluation_results.items():
            mape = results['accuracy_metrics'].get('mape', 0)
            rmse = results['accuracy_metrics'].get('rmse', 0)
            r2 = results['accuracy_metrics'].get('r2', 0)
            direction_acc = results['directional_metrics'].get('direction_accuracy', 0)
            stability = results['robustness_metrics'].get('stability_score', 0)
            profit = results['economic_metrics'].get('profit_score', 0)
            
            html += f"""
            <tr>
                <td><strong>{model_name}</strong></td>
                <td>{mape:.4f}</td>
                <td>{rmse:.4f}</td>
                <td>{r2:.4f}</td>
                <td>{direction_acc:.4f}</td>
                <td>{stability:.4f}</td>
                <td>{profit:.4f}</td>
            </tr>
            """
        
        html += "</table>"
        return html
    
    def _generate_key_findings_html(self, evaluation_results: Dict, ranked_models: List) -> str:
        """生成关键发现HTML"""
        findings = []
        
        if ranked_models:
            best_model = ranked_models[0][0]
            findings.append(f"• {best_model} 在综合评估中表现最佳")
        
        # 分析各个维度的最佳模型
        best_accuracy = max(evaluation_results.items(), 
                          key=lambda x: 1 - x[1]['accuracy_metrics'].get('mape', 1))
        findings.append(f"• {best_accuracy[0]} 在预测准确性方面表现最佳")
        
        best_direction = max(evaluation_results.items(),
                           key=lambda x: x[1]['directional_metrics'].get('direction_accuracy', 0))
        findings.append(f"• {best_direction[0]} 在方向预测方面表现最佳")
        
        best_stability = max(evaluation_results.items(),
                           key=lambda x: x[1]['robustness_metrics'].get('stability_score', 0))
        findings.append(f"• {best_stability[0]} 在稳定性方面表现最佳")
        
        html = "<ul>"
        for finding in findings:
            html += f"<li>{finding}</li>"
        html += "</ul>"
        
        return html
    
    def _generate_recommendations_html(self, evaluation_results: Dict, ranked_models: List) -> str:
        """生成建议HTML"""
        recommendations = [
            "• 建议在生产环境中部署排名前两位的模型进行集成预测",
            "• 定期监控模型性能，特别关注MAPE和方向准确率指标",
            "• 考虑根据不同市场条件动态选择最适合的模型",
            "• 建议每周重新训练模型以适应市场变化",
            "• 加强极端市场条件下的模型鲁棒性测试"
        ]
        
        html = "<ul>"
        for rec in recommendations:
            html += f"<li>{rec}</li>"
        html += "</ul>"
        
        return html


def main():
    """主函数"""
    evaluator = PowerMarketModelEvaluator()
    
    try:
        # 运行评估流水线
        summary = evaluator.run_evaluation_pipeline()
        print(f"模型评估完成，摘要: {summary}")
        
    except Exception as e:
        logger.error(f"模型评估失败: {str(e)}")


if __name__ == "__main__":
    main()
