"""
电力现货市场预测 ML 流水线 - 测试脚本
作者：Gregor
版本：v0.1-MVP
日期：2025-08-25

功能：
1. 测试各个模块的基本功能
2. 验证流水线完整性
3. 生成测试数据
4. 模拟API调用
"""

import asyncio
import logging
import pandas as pd
import numpy as np
import sqlite3
import json
import requests
from datetime import datetime, timedelta
from pathlib import Path
import yaml

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PipelineTester:
    """流水线测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.test_data_path = Path("data/raw")
        self.test_data_path.mkdir(parents=True, exist_ok=True)
        logger.info("流水线测试器初始化完成")
    
    def generate_test_data(self, days: int = 7) -> bool:
        """生成测试数据"""
        logger.info(f"生成 {days} 天的测试数据")
        
        try:
            # 创建时间序列
            end_time = datetime.now()
            start_time = end_time - timedelta(days=days)
            time_range = pd.date_range(start=start_time, end=end_time, freq='15T')
            
            # 生成模拟数据
            n_points = len(time_range)
            
            # 市场数据
            base_price = 50.0
            price_trend = np.sin(np.arange(n_points) * 2 * np.pi / 96) * 10  # 日周期
            price_noise = np.random.normal(0, 5, n_points)
            prices = base_price + price_trend + price_noise
            prices = np.maximum(prices, 10)  # 价格不能为负
            
            volumes = np.random.normal(1000, 200, n_points)
            volumes = np.maximum(volumes, 100)
            
            bid_ask_spreads = np.random.normal(2, 0.5, n_points)
            order_flow_imbalances = np.random.normal(0, 10, n_points)
            market_depths = np.random.normal(500, 100, n_points)
            
            # 天气数据
            base_temp = 20.0
            temp_seasonal = np.sin(np.arange(n_points) * 2 * np.pi / (96 * 365)) * 15
            temp_daily = np.sin(np.arange(n_points) * 2 * np.pi / 96) * 5
            temp_noise = np.random.normal(0, 2, n_points)
            temperatures = base_temp + temp_seasonal + temp_daily + temp_noise
            
            humidity = np.random.normal(60, 15, n_points)
            humidity = np.clip(humidity, 0, 100)
            
            wind_speeds = np.random.exponential(5, n_points)
            wind_speeds = np.clip(wind_speeds, 0, 30)
            
            solar_irradiances = np.maximum(0, np.sin(np.arange(n_points) * 2 * np.pi / 96) * 800 + np.random.normal(0, 100, n_points))
            
            # 负荷数据
            base_load = 1000.0
            load_seasonal = temperatures * 10  # 温度影响负荷
            load_daily = np.sin(np.arange(n_points) * 2 * np.pi / 96) * 200
            load_noise = np.random.normal(0, 50, n_points)
            total_loads = base_load + load_seasonal + load_daily + load_noise
            total_loads = np.maximum(total_loads, 500)
            
            # 发电数据
            total_generations = total_loads + np.random.normal(50, 20, n_points)  # 略高于负荷
            coal_generations = total_generations * 0.4 + np.random.normal(0, 50, n_points)
            gas_generations = total_generations * 0.3 + np.random.normal(0, 30, n_points)
            renewable_generations = total_generations * 0.2 + np.random.normal(0, 40, n_points)
            nuclear_generations = total_generations * 0.1 + np.random.normal(0, 10, n_points)
            
            fuel_costs = np.random.normal(30, 5, n_points)
            
            # 创建数据库
            db_path = self.test_data_path / "power_market.db"
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # 创建表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS market_data (
                    timestamp DATETIME PRIMARY KEY,
                    price REAL,
                    volume REAL,
                    bid_ask_spread REAL,
                    order_flow_imbalance REAL,
                    market_depth REAL,
                    data_quality_score REAL
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS weather_data (
                    timestamp DATETIME PRIMARY KEY,
                    temperature REAL,
                    humidity REAL,
                    wind_speed REAL,
                    solar_irradiance REAL,
                    extreme_weather_alert INTEGER,
                    data_quality_score REAL
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS load_data (
                    timestamp DATETIME PRIMARY KEY,
                    total_load REAL,
                    load_change_rate REAL,
                    industrial_load REAL,
                    residential_load REAL,
                    commercial_load REAL,
                    data_quality_score REAL
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS generation_data (
                    timestamp DATETIME PRIMARY KEY,
                    total_generation REAL,
                    coal_generation REAL,
                    gas_generation REAL,
                    renewable_generation REAL,
                    nuclear_generation REAL,
                    fuel_cost REAL,
                    data_quality_score REAL
                )
            ''')
            
            # 插入数据
            for i, timestamp in enumerate(time_range):
                # 市场数据
                cursor.execute('''
                    INSERT OR REPLACE INTO market_data 
                    (timestamp, price, volume, bid_ask_spread, order_flow_imbalance, market_depth, data_quality_score)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    timestamp.isoformat(),
                    prices[i],
                    volumes[i],
                    bid_ask_spreads[i],
                    order_flow_imbalances[i],
                    market_depths[i],
                    0.95
                ))
                
                # 天气数据
                cursor.execute('''
                    INSERT OR REPLACE INTO weather_data 
                    (timestamp, temperature, humidity, wind_speed, solar_irradiance, extreme_weather_alert, data_quality_score)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    timestamp.isoformat(),
                    temperatures[i],
                    humidity[i],
                    wind_speeds[i],
                    solar_irradiances[i],
                    0,
                    0.95
                ))
                
                # 负荷数据
                cursor.execute('''
                    INSERT OR REPLACE INTO load_data 
                    (timestamp, total_load, load_change_rate, industrial_load, residential_load, commercial_load, data_quality_score)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    timestamp.isoformat(),
                    total_loads[i],
                    0.0 if i == 0 else (total_loads[i] - total_loads[i-1]) / total_loads[i-1],
                    total_loads[i] * 0.4,
                    total_loads[i] * 0.3,
                    total_loads[i] * 0.3,
                    0.95
                ))
                
                # 发电数据
                cursor.execute('''
                    INSERT OR REPLACE INTO generation_data 
                    (timestamp, total_generation, coal_generation, gas_generation, renewable_generation, nuclear_generation, fuel_cost, data_quality_score)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    timestamp.isoformat(),
                    total_generations[i],
                    coal_generations[i],
                    gas_generations[i],
                    renewable_generations[i],
                    nuclear_generations[i],
                    fuel_costs[i],
                    0.95
                ))
            
            conn.commit()
            conn.close()
            
            logger.info(f"测试数据生成完成，共 {n_points} 个数据点")
            return True
            
        except Exception as e:
            logger.error(f"生成测试数据失败: {str(e)}")
            return False
    
    def test_individual_modules(self) -> Dict[str, bool]:
        """测试各个模块"""
        logger.info("开始测试各个模块")
        
        results = {}
        
        # 测试数据预处理
        try:
            from step2_data_preprocessor import PowerMarketDataPreprocessor
            preprocessor = PowerMarketDataPreprocessor()
            summary = preprocessor.run_preprocessing_pipeline()
            results['data_preprocessing'] = summary is not None
            logger.info("数据预处理模块测试通过")
        except Exception as e:
            logger.error(f"数据预处理模块测试失败: {str(e)}")
            results['data_preprocessing'] = False
        
        # 测试特征工程
        try:
            from step3_feature_engineer import PowerMarketFeatureEngineer
            engineer = PowerMarketFeatureEngineer()
            summary = engineer.run_feature_engineering_pipeline()
            results['feature_engineering'] = summary is not None
            logger.info("特征工程模块测试通过")
        except Exception as e:
            logger.error(f"特征工程模块测试失败: {str(e)}")
            results['feature_engineering'] = False
        
        # 测试模型训练
        try:
            from step4_model_trainer import PowerMarketModelTrainer
            trainer = PowerMarketModelTrainer()
            summary = trainer.run_training_pipeline()
            results['model_training'] = summary is not None
            logger.info("模型训练模块测试通过")
        except Exception as e:
            logger.error(f"模型训练模块测试失败: {str(e)}")
            results['model_training'] = False
        
        # 测试模型评估
        try:
            from step5_model_evaluator import PowerMarketModelEvaluator
            evaluator = PowerMarketModelEvaluator()
            summary = evaluator.run_evaluation_pipeline()
            results['model_evaluation'] = summary is not None
            logger.info("模型评估模块测试通过")
        except Exception as e:
            logger.error(f"模型评估模块测试失败: {str(e)}")
            results['model_evaluation'] = False
        
        return results
    
    async def test_full_pipeline(self) -> bool:
        """测试完整流水线"""
        logger.info("开始测试完整流水线")
        
        try:
            from step8_pipeline_controller import PowerMarketMLPipeline
            
            pipeline = PowerMarketMLPipeline()
            
            # 只运行前几个步骤，避免部署
            steps_to_run = [
                'data_collection',
                'data_preprocessing', 
                'feature_engineering',
                'model_training',
                'model_evaluation'
            ]
            
            summary = await pipeline.run_pipeline(steps_to_run)
            
            success = summary['status'] == 'completed'
            logger.info(f"完整流水线测试{'通过' if success else '失败'}")
            
            return success
            
        except Exception as e:
            logger.error(f"完整流水线测试失败: {str(e)}")
            return False
    
    def test_api_endpoints(self, base_url: str = "http://localhost:8000") -> Dict[str, bool]:
        """测试API端点"""
        logger.info("开始测试API端点")
        
        results = {}
        
        # 测试健康检查
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            results['health_check'] = response.status_code == 200
            logger.info("健康检查API测试通过")
        except Exception as e:
            logger.error(f"健康检查API测试失败: {str(e)}")
            results['health_check'] = False
        
        # 测试模型列表
        try:
            response = requests.get(f"{base_url}/models", timeout=5)
            results['models_list'] = response.status_code == 200
            logger.info("模型列表API测试通过")
        except Exception as e:
            logger.error(f"模型列表API测试失败: {str(e)}")
            results['models_list'] = False
        
        # 测试预测接口
        try:
            test_request = {
                "features": {
                    "temperature": 25.0,
                    "humidity": 60.0,
                    "wind_speed": 5.0,
                    "total_load": 1000.0,
                    "hour": 14,
                    "day_of_week": 1
                },
                "prediction_horizon": 1,
                "include_uncertainty": False
            }
            
            response = requests.post(
                f"{base_url}/predict", 
                json=test_request,
                timeout=10
            )
            results['prediction'] = response.status_code == 200
            
            if response.status_code == 200:
                prediction_data = response.json()
                logger.info(f"预测API测试通过，预测值: {prediction_data.get('predictions', [])}")
            else:
                logger.error(f"预测API返回错误: {response.status_code}")
                
        except Exception as e:
            logger.error(f"预测API测试失败: {str(e)}")
            results['prediction'] = False
        
        return results
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合测试"""
        logger.info("开始运行综合测试")
        
        test_results = {
            'timestamp': datetime.now().isoformat(),
            'test_data_generation': False,
            'individual_modules': {},
            'full_pipeline': False,
            'api_endpoints': {},
            'overall_success': False
        }
        
        try:
            # 1. 生成测试数据
            test_results['test_data_generation'] = self.generate_test_data(days=3)
            
            # 2. 测试各个模块
            if test_results['test_data_generation']:
                test_results['individual_modules'] = self.test_individual_modules()
            
            # 3. 测试完整流水线
            if any(test_results['individual_modules'].values()):
                test_results['full_pipeline'] = asyncio.run(self.test_full_pipeline())
            
            # 4. 测试API端点（如果服务正在运行）
            test_results['api_endpoints'] = self.test_api_endpoints()
            
            # 5. 计算总体成功率
            module_success_rate = sum(test_results['individual_modules'].values()) / max(len(test_results['individual_modules']), 1)
            api_success_rate = sum(test_results['api_endpoints'].values()) / max(len(test_results['api_endpoints']), 1)
            
            test_results['overall_success'] = (
                test_results['test_data_generation'] and
                module_success_rate >= 0.8 and  # 至少80%的模块测试通过
                (test_results['full_pipeline'] or api_success_rate >= 0.5)  # 流水线通过或API部分可用
            )
            
            # 保存测试结果
            results_path = Path("test_results.json")
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(test_results, f, ensure_ascii=False, indent=2)
            
            logger.info(f"综合测试完成，结果已保存到: {results_path}")
            
        except Exception as e:
            logger.error(f"综合测试执行失败: {str(e)}")
            test_results['error'] = str(e)
        
        return test_results


def main():
    """主函数"""
    tester = PipelineTester()
    
    print("="*60)
    print("电力现货市场预测 ML 流水线 - 综合测试")
    print("="*60)
    
    # 运行综合测试
    results = tester.run_comprehensive_test()
    
    # 打印测试结果
    print("\n测试结果摘要:")
    print("-"*40)
    print(f"测试数据生成: {'✅' if results['test_data_generation'] else '❌'}")
    
    print("\n模块测试结果:")
    for module, success in results['individual_modules'].items():
        print(f"  {module}: {'✅' if success else '❌'}")
    
    print(f"\n完整流水线测试: {'✅' if results['full_pipeline'] else '❌'}")
    
    print("\nAPI端点测试结果:")
    for endpoint, success in results['api_endpoints'].items():
        print(f"  {endpoint}: {'✅' if success else '❌'}")
    
    print(f"\n总体测试结果: {'✅ 通过' if results['overall_success'] else '❌ 失败'}")
    
    if results['overall_success']:
        print("\n🎉 恭喜！电力现货市场预测ML流水线测试全部通过！")
        print("\n下一步操作:")
        print("1. 配置真实的数据源API")
        print("2. 调整模型参数以适应实际数据")
        print("3. 部署到生产环境")
        print("4. 设置监控和告警")
    else:
        print("\n⚠️  部分测试失败，请检查错误日志并修复问题。")
    
    print(f"\n详细测试结果已保存到: test_results.json")


if __name__ == "__main__":
    main()
