"""
电力现货市场预测 ML 流水线 - 模型训练模块
作者：Gregor
版本：v0.1-MVP
日期：2025-08-25

功能：
1. 多尺度预测模型训练
2. Enhanced TFT模型实现
3. 模型集成策略
4. 超参数优化
5. 模型评估和保存
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import yaml
import logging
import joblib
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, mean_absolute_percentage_error
import xgboost as xgb
import lightgbm as lgb
import optuna
from pytorch_tabnet.tab_model import TabNetRegressor
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PowerMarketModelTrainer:
    """电力市场模型训练器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """初始化模型训练器"""
        self.config = self._load_config(config_path)
        self.feature_store_path = Path(self.config['data']['storage']['feature_store_path'])
        self.model_data_path = Path(self.config['data']['storage']['model_data_path'])
        self.model_data_path.mkdir(parents=True, exist_ok=True)
        
        # 设置设备
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"使用设备: {self.device}")
        
        # 初始化模型训练器
        self.short_term_trainer = ShortTermModelTrainer(self.config, self.device)
        self.model_evaluator = ModelEvaluator(self.config)
        self.hyperparameter_optimizer = HyperparameterOptimizer(self.config)
        
        logger.info("模型训练器初始化完成")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def load_feature_data(self) -> Dict[str, pd.DataFrame]:
        """加载特征数据"""
        data_splits = {}
        
        for split_name in ['train', 'validation', 'test']:
            file_path = self.feature_store_path / f"{split_name}_features.parquet"
            if file_path.exists():
                df = pd.read_parquet(file_path)
                data_splits[split_name] = df
                logger.info(f"加载 {split_name} 特征数据: {len(df)} 条记录, {len(df.columns)} 个特征")
            else:
                logger.warning(f"文件不存在: {file_path}")
        
        return data_splits
    
    def prepare_training_data(self, data_splits: Dict[str, pd.DataFrame], target_column: str = 'price') -> Dict:
        """准备训练数据"""
        logger.info("准备训练数据")
        
        prepared_data = {}
        
        for split_name, df in data_splits.items():
            if target_column not in df.columns:
                logger.error(f"目标列 {target_column} 不存在于 {split_name} 数据中")
                continue
            
            # 分离特征和目标
            feature_columns = [col for col in df.columns if col != target_column]
            X = df[feature_columns].fillna(0).values
            y = df[target_column].fillna(0).values
            
            prepared_data[split_name] = {
                'X': X,
                'y': y,
                'feature_names': feature_columns,
                'timestamps': df.index
            }
            
            logger.info(f"{split_name} 数据准备完成: X shape {X.shape}, y shape {y.shape}")
        
        return prepared_data
    
    def train_short_term_models(self, prepared_data: Dict) -> Dict:
        """训练短期预测模型"""
        logger.info("开始训练短期预测模型")
        
        if 'train' not in prepared_data or 'validation' not in prepared_data:
            logger.error("缺少训练或验证数据")
            return {}
        
        # 训练各种模型
        models = self.short_term_trainer.train_models(
            prepared_data['train'], 
            prepared_data['validation']
        )
        
        logger.info("短期预测模型训练完成")
        return models
    
    def evaluate_models(self, models: Dict, prepared_data: Dict) -> Dict:
        """评估模型性能"""
        logger.info("开始模型评估")
        
        evaluation_results = {}
        
        for split_name in ['validation', 'test']:
            if split_name not in prepared_data:
                continue
            
            split_results = {}
            X = prepared_data[split_name]['X']
            y_true = prepared_data[split_name]['y']
            
            for model_name, model in models.items():
                try:
                    # 预测
                    if hasattr(model, 'predict'):
                        y_pred = model.predict(X)
                    else:
                        # 对于PyTorch模型
                        model.eval()
                        with torch.no_grad():
                            X_tensor = torch.FloatTensor(X).to(self.device)
                            y_pred = model(X_tensor).cpu().numpy().flatten()
                    
                    # 计算评估指标
                    metrics = self.model_evaluator.calculate_metrics(y_true, y_pred)
                    split_results[model_name] = metrics
                    
                    logger.info(f"{split_name} - {model_name}: MAPE={metrics['mape']:.4f}, RMSE={metrics['rmse']:.4f}")
                    
                except Exception as e:
                    logger.error(f"评估 {model_name} 在 {split_name} 上失败: {str(e)}")
            
            evaluation_results[split_name] = split_results
        
        return evaluation_results
    
    def save_models(self, models: Dict, evaluation_results: Dict):
        """保存模型和评估结果"""
        logger.info("保存模型和评估结果")
        
        # 保存模型
        for model_name, model in models.items():
            model_path = self.model_data_path / f"{model_name}_model.joblib"
            try:
                if hasattr(model, 'state_dict'):
                    # PyTorch模型
                    torch.save(model.state_dict(), model_path.with_suffix('.pth'))
                else:
                    # Scikit-learn模型
                    joblib.dump(model, model_path)
                logger.info(f"保存模型: {model_path}")
            except Exception as e:
                logger.error(f"保存模型 {model_name} 失败: {str(e)}")
        
        # 保存评估结果
        results_path = self.model_data_path / "evaluation_results.joblib"
        joblib.dump(evaluation_results, results_path)
        logger.info(f"保存评估结果: {results_path}")
    
    def run_training_pipeline(self):
        """运行完整的模型训练流水线"""
        logger.info("开始运行模型训练流水线")
        
        try:
            # 1. 加载特征数据
            data_splits = self.load_feature_data()
            if not data_splits:
                logger.error("无法加载特征数据，训练流水线终止")
                return
            
            # 2. 准备训练数据
            prepared_data = self.prepare_training_data(data_splits)
            if not prepared_data:
                logger.error("数据准备失败，训练流水线终止")
                return
            
            # 3. 训练短期预测模型
            models = self.train_short_term_models(prepared_data)
            if not models:
                logger.error("模型训练失败，训练流水线终止")
                return
            
            # 4. 评估模型
            evaluation_results = self.evaluate_models(models, prepared_data)
            
            # 5. 保存模型和结果
            self.save_models(models, evaluation_results)
            
            logger.info("模型训练流水线完成")
            
            # 返回训练摘要
            summary = {
                'trained_models': list(models.keys()),
                'best_model': self._find_best_model(evaluation_results),
                'evaluation_results': evaluation_results
            }
            
            logger.info(f"训练摘要: {summary}")
            return summary
            
        except Exception as e:
            logger.error(f"模型训练流水线执行失败: {str(e)}")
            raise
    
    def _find_best_model(self, evaluation_results: Dict) -> str:
        """找到最佳模型"""
        if 'validation' not in evaluation_results:
            return "unknown"
        
        best_model = None
        best_mape = float('inf')
        
        for model_name, metrics in evaluation_results['validation'].items():
            if metrics.get('mape', float('inf')) < best_mape:
                best_mape = metrics['mape']
                best_model = model_name
        
        return best_model or "unknown"


class ShortTermModelTrainer:
    """短期模型训练器"""
    
    def __init__(self, config: Dict, device: torch.device):
        self.config = config
        self.device = device
    
    def train_models(self, train_data: Dict, val_data: Dict) -> Dict:
        """训练所有短期预测模型"""
        models = {}
        
        # 训练XGBoost
        try:
            xgb_model = self._train_xgboost(train_data, val_data)
            models['xgboost'] = xgb_model
            logger.info("XGBoost模型训练完成")
        except Exception as e:
            logger.error(f"XGBoost训练失败: {str(e)}")
        
        # 训练LightGBM
        try:
            lgb_model = self._train_lightgbm(train_data, val_data)
            models['lightgbm'] = lgb_model
            logger.info("LightGBM模型训练完成")
        except Exception as e:
            logger.error(f"LightGBM训练失败: {str(e)}")
        
        # 训练TabNet
        try:
            tabnet_model = self._train_tabnet(train_data, val_data)
            models['tabnet'] = tabnet_model
            logger.info("TabNet模型训练完成")
        except Exception as e:
            logger.error(f"TabNet训练失败: {str(e)}")
        
        # 训练Enhanced TFT（简化版）
        try:
            tft_model = self._train_enhanced_tft(train_data, val_data)
            models['enhanced_tft'] = tft_model
            logger.info("Enhanced TFT模型训练完成")
        except Exception as e:
            logger.error(f"Enhanced TFT训练失败: {str(e)}")
        
        return models
    
    def _train_xgboost(self, train_data: Dict, val_data: Dict) -> xgb.XGBRegressor:
        """训练XGBoost模型"""
        config = self.config['models']['short_term']['xgboost']
        
        model = xgb.XGBRegressor(
            n_estimators=config['n_estimators'],
            max_depth=config['max_depth'],
            learning_rate=config['learning_rate'],
            subsample=config['subsample'],
            colsample_bytree=config['colsample_bytree'],
            random_state=42
        )
        
        model.fit(
            train_data['X'], train_data['y'],
            eval_set=[(val_data['X'], val_data['y'])],
            early_stopping_rounds=10,
            verbose=False
        )
        
        return model
    
    def _train_lightgbm(self, train_data: Dict, val_data: Dict) -> lgb.LGBMRegressor:
        """训练LightGBM模型"""
        config = self.config['models']['short_term']['lightgbm']
        
        model = lgb.LGBMRegressor(
            n_estimators=config['n_estimators'],
            max_depth=config['max_depth'],
            learning_rate=config['learning_rate'],
            num_leaves=config['num_leaves'],
            feature_fraction=config['feature_fraction'],
            random_state=42,
            verbose=-1
        )
        
        model.fit(
            train_data['X'], train_data['y'],
            eval_set=[(val_data['X'], val_data['y'])],
            callbacks=[lgb.early_stopping(10), lgb.log_evaluation(0)]
        )
        
        return model
    
    def _train_tabnet(self, train_data: Dict, val_data: Dict) -> TabNetRegressor:
        """训练TabNet模型"""
        model = TabNetRegressor(
            n_d=64, n_a=64,
            n_steps=5,
            gamma=1.3,
            lambda_sparse=1e-3,
            optimizer_fn=torch.optim.Adam,
            optimizer_params=dict(lr=2e-2),
            mask_type='entmax',
            scheduler_params={"step_size": 10, "gamma": 0.9},
            scheduler_fn=torch.optim.lr_scheduler.StepLR,
            verbose=0
        )
        
        model.fit(
            train_data['X'], train_data['y'].reshape(-1, 1),
            eval_set=[(val_data['X'], val_data['y'].reshape(-1, 1))],
            max_epochs=100,
            patience=10,
            batch_size=256,
            virtual_batch_size=128,
            num_workers=0,
            drop_last=False
        )
        
        return model
    
    def _train_enhanced_tft(self, train_data: Dict, val_data: Dict) -> nn.Module:
        """训练Enhanced TFT模型（简化版）"""
        config = self.config['models']['short_term']['enhanced_tft']
        
        # 创建简化的TFT模型
        input_size = train_data['X'].shape[1]
        model = SimplifiedTFT(
            input_size=input_size,
            hidden_size=config['hidden_size'],
            num_layers=config['num_layers'],
            dropout=config['dropout']
        ).to(self.device)
        
        # 准备数据加载器
        train_dataset = TensorDataset(
            torch.FloatTensor(train_data['X']),
            torch.FloatTensor(train_data['y'])
        )
        val_dataset = TensorDataset(
            torch.FloatTensor(val_data['X']),
            torch.FloatTensor(val_data['y'])
        )
        
        train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False)
        
        # 训练模型
        optimizer = optim.Adam(model.parameters(), lr=config['learning_rate'])
        criterion = nn.MSELoss()
        
        best_val_loss = float('inf')
        patience_counter = 0
        
        for epoch in range(config['max_epochs']):
            # 训练阶段
            model.train()
            train_loss = 0
            for batch_X, batch_y in train_loader:
                batch_X, batch_y = batch_X.to(self.device), batch_y.to(self.device)
                
                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs.squeeze(), batch_y)
                loss.backward()
                optimizer.step()
                
                train_loss += loss.item()
            
            # 验证阶段
            model.eval()
            val_loss = 0
            with torch.no_grad():
                for batch_X, batch_y in val_loader:
                    batch_X, batch_y = batch_X.to(self.device), batch_y.to(self.device)
                    outputs = model(batch_X)
                    loss = criterion(outputs.squeeze(), batch_y)
                    val_loss += loss.item()
            
            val_loss /= len(val_loader)
            
            # 早停检查
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
            else:
                patience_counter += 1
                if patience_counter >= 10:
                    logger.info(f"早停于第 {epoch+1} 轮")
                    break
            
            if (epoch + 1) % 10 == 0:
                logger.info(f"Epoch {epoch+1}: Train Loss={train_loss/len(train_loader):.4f}, Val Loss={val_loss:.4f}")
        
        return model


class SimplifiedTFT(nn.Module):
    """简化的TFT模型"""
    
    def __init__(self, input_size: int, hidden_size: int, num_layers: int, dropout: float):
        super(SimplifiedTFT, self).__init__()
        
        self.input_size = input_size
        self.hidden_size = hidden_size
        
        # 特征选择网络
        self.feature_selection = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size, input_size),
            nn.Sigmoid()
        )
        
        # LSTM层
        self.lstm = nn.LSTM(
            input_size, hidden_size, 
            num_layers=num_layers, 
            dropout=dropout if num_layers > 1 else 0,
            batch_first=True
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(
            embed_dim=hidden_size,
            num_heads=8,
            dropout=dropout,
            batch_first=True
        )
        
        # 输出层
        self.output_layer = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_size // 2, 1)
        )
    
    def forward(self, x):
        batch_size = x.size(0)
        
        # 特征选择
        feature_weights = self.feature_selection(x)
        x_selected = x * feature_weights
        
        # 重塑为序列格式（假设序列长度为1）
        x_seq = x_selected.unsqueeze(1)  # (batch_size, 1, input_size)
        
        # LSTM处理
        lstm_out, _ = self.lstm(x_seq)  # (batch_size, 1, hidden_size)
        
        # 注意力机制
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
        
        # 输出预测
        output = self.output_layer(attn_out.squeeze(1))  # (batch_size, 1)
        
        return output


class ModelEvaluator:
    """模型评估器"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def calculate_metrics(self, y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
        """计算评估指标"""
        # 移除NaN值
        mask = ~(np.isnan(y_true) | np.isnan(y_pred))
        y_true_clean = y_true[mask]
        y_pred_clean = y_pred[mask]
        
        if len(y_true_clean) == 0:
            return {'mape': float('inf'), 'rmse': float('inf'), 'mae': float('inf')}
        
        metrics = {}
        
        # 准确性指标
        metrics['mae'] = mean_absolute_error(y_true_clean, y_pred_clean)
        metrics['rmse'] = np.sqrt(mean_squared_error(y_true_clean, y_pred_clean))
        
        # MAPE（避免除零）
        mape_mask = y_true_clean != 0
        if np.sum(mape_mask) > 0:
            metrics['mape'] = np.mean(np.abs((y_true_clean[mape_mask] - y_pred_clean[mape_mask]) / y_true_clean[mape_mask]))
        else:
            metrics['mape'] = float('inf')
        
        # 方向性指标
        if len(y_true_clean) > 1:
            true_direction = np.diff(y_true_clean) > 0
            pred_direction = np.diff(y_pred_clean) > 0
            metrics['direction_accuracy'] = np.mean(true_direction == pred_direction)
        else:
            metrics['direction_accuracy'] = 0.0
        
        # R²分数
        ss_res = np.sum((y_true_clean - y_pred_clean) ** 2)
        ss_tot = np.sum((y_true_clean - np.mean(y_true_clean)) ** 2)
        metrics['r2'] = 1 - (ss_res / (ss_tot + 1e-8))
        
        return metrics


class HyperparameterOptimizer:
    """超参数优化器"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def optimize_xgboost(self, train_data: Dict, val_data: Dict) -> Dict:
        """优化XGBoost超参数"""
        def objective(trial):
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
            }
            
            model = xgb.XGBRegressor(**params, random_state=42)
            model.fit(train_data['X'], train_data['y'])
            
            y_pred = model.predict(val_data['X'])
            mape = mean_absolute_percentage_error(val_data['y'], y_pred)
            
            return mape
        
        study = optuna.create_study(direction='minimize')
        study.optimize(objective, n_trials=50)
        
        return study.best_params


def main():
    """主函数"""
    trainer = PowerMarketModelTrainer()
    
    try:
        # 运行训练流水线
        summary = trainer.run_training_pipeline()
        print(f"模型训练完成，摘要: {summary}")
        
    except Exception as e:
        logger.error(f"模型训练失败: {str(e)}")


if __name__ == "__main__":
    main()
