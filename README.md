# 电力现货市场预测 ML 流水线

基于机器学习的电力现货市场价格预测系统，实现从数据采集到模型部署的完整MLOps流水线。

## 项目概述

本项目实现了一个完整的电力现货市场预测ML流水线，包括：

- **多源数据采集**：市场数据、天气数据、负荷数据、机组数据等
- **智能数据预处理**：异常检测、缺失值处理、数据标准化
- **高级特征工程**：时间特征、市场特征、交互特征等
- **多模型集成**：XGBoost、LightGBM、TabNet、Enhanced TFT等
- **全面模型评估**：准确性、方向性、鲁棒性、经济性等多维度评估
- **自动化部署**：FastAPI服务、Docker容器化、监控告警
- **持续监控**：模型漂移检测、性能监控、自动重训练

## 项目结构

```
ElectricalEnergyMarket/
├── config.yaml                           # 配置文件
├── requirements.txt                       # 依赖包
├── README.md                             # 项目说明
├── 电力现货市场预测 ML 流水线设计文档.md    # 设计文档
├── step1_data_collector.py               # 数据采集模块
├── step2_data_preprocessor.py            # 数据预处理模块
├── step3_feature_engineer.py             # 特征工程模块
├── step4_model_trainer.py                # 模型训练模块
├── step5_model_evaluator.py              # 模型评估模块
├── step6_model_deployer.py               # 模型部署模块
├── step7_model_monitor.py                # 模型监控模块
├── step8_pipeline_controller.py          # 流水线控制器
├── data/                                 # 数据目录
│   ├── raw/                             # 原始数据
│   ├── processed/                       # 预处理数据
│   ├── features/                        # 特征数据
│   └── model/                           # 模型数据
├── evaluation_results/                   # 评估结果
├── monitoring_results/                   # 监控结果
└── pipeline_results/                     # 流水线结果
```

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd ElectricalEnergyMarket

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

编辑 `config.yaml` 文件，配置数据源、模型参数等：

```yaml
# 数据源配置
data:
  sources:
    market_data:
      api_endpoint: "https://your-market-api.com/v1"
      # ... 其他配置
```

### 3. 运行流水线

#### 方式一：运行完整流水线

```bash
python step8_pipeline_controller.py
```

#### 方式二：分步骤运行

```bash
# 1. 数据采集
python step1_data_collector.py

# 2. 数据预处理
python step2_data_preprocessor.py

# 3. 特征工程
python step3_feature_engineer.py

# 4. 模型训练
python step4_model_trainer.py

# 5. 模型评估
python step5_model_evaluator.py

# 6. 模型部署
python step6_model_deployer.py

# 7. 模型监控
python step7_model_monitor.py
```

### 4. 启动预测服务

```bash
# 启动API服务
python step6_model_deployer.py

# 或使用Docker
docker-compose up -d
```

## 核心功能

### 数据采集 (step1_data_collector.py)

- 多源数据并行采集
- 实时数据质量检查
- 自动化数据存储
- 异常处理和重试机制

```python
from step1_data_collector import DataCollector

collector = DataCollector()
await collector.run_collection_cycle()
```

### 数据预处理 (step2_data_preprocessor.py)

- 电力市场特有异常检测
- 智能缺失值处理
- 多策略数据标准化
- 时序数据分割

```python
from step2_data_preprocessor import PowerMarketDataPreprocessor

preprocessor = PowerMarketDataPreprocessor()
summary = preprocessor.run_preprocessing_pipeline()
```

### 特征工程 (step3_feature_engineer.py)

- 时间特征（周期性编码、节假日标识）
- 市场特征（价格波动、供需比、市场深度）
- 滞后特征和滚动窗口特征
- 自动特征选择（Boruta、SHAP）

```python
from step3_feature_engineer import PowerMarketFeatureEngineer

engineer = PowerMarketFeatureEngineer()
summary = engineer.run_feature_engineering_pipeline()
```

### 模型训练 (step4_model_trainer.py)

- 多模型并行训练
- 超参数自动优化
- 模型集成策略
- 不确定性量化

```python
from step4_model_trainer import PowerMarketModelTrainer

trainer = PowerMarketModelTrainer()
summary = trainer.run_training_pipeline()
```

### 模型评估 (step5_model_evaluator.py)

- 多维度评估指标
- 可视化评估报告
- 模型性能排序
- 经济性评估

```python
from step5_model_evaluator import PowerMarketModelEvaluator

evaluator = PowerMarketModelEvaluator()
summary = evaluator.run_evaluation_pipeline()
```

### 模型部署 (step6_model_deployer.py)

- FastAPI REST API
- 实时预测服务
- 缓存和性能优化
- Prometheus监控

```python
# API使用示例
import requests

response = requests.post("http://localhost:8000/predict", json={
    "features": {
        "temperature": 25.0,
        "load": 1000.0,
        "hour": 14
    },
    "prediction_horizon": 4,
    "include_uncertainty": True
})

prediction = response.json()
```

### 模型监控 (step7_model_monitor.py)

- 实时性能监控
- 数据漂移检测
- 自动告警系统
- 监控报告生成

```python
from step7_model_monitor import PowerMarketModelMonitor

monitor = PowerMarketModelMonitor()
monitor.run_monitoring_cycle()
```

## API 接口

### 预测接口

```bash
POST /predict
Content-Type: application/json

{
    "features": {
        "temperature": 25.0,
        "humidity": 60.0,
        "wind_speed": 5.0,
        "total_load": 1000.0,
        "hour": 14,
        "day_of_week": 1
    },
    "model_name": "xgboost",
    "prediction_horizon": 4,
    "include_uncertainty": true
}
```

### 模型信息接口

```bash
GET /models
```

### 健康检查接口

```bash
GET /health
```

### 监控指标接口

```bash
GET /metrics
```

## 配置说明

### 数据配置

```yaml
data:
  collection_frequency: 15  # 数据采集频率（分钟）
  sources:
    market_data:
      api_endpoint: "https://api.powermarket.com/v1"
      update_frequency: 15
      fields: ["price", "volume", "clearing_results"]
```

### 模型配置

```yaml
models:
  short_term:
    primary_model: "enhanced_tft"
    auxiliary_models: ["xgboost", "tabnet", "lightgbm"]
    ensemble_method: "dynamic_weighted"
```

### 部署配置

```yaml
deployment:
  api:
    host: "0.0.0.0"
    port: 8000
    workers: 4
  performance_targets:
    p99_latency: 50  # ms
    max_qps: 1000
```

## 监控和告警

### Prometheus 指标

- `predictions_total`: 预测请求总数
- `prediction_duration_seconds`: 预测处理时间
- `model_accuracy`: 模型准确性指标
- `cache_hits_total`: 缓存命中次数

### Grafana 仪表板

项目包含预配置的Grafana仪表板，用于可视化：

- 模型性能趋势
- API响应时间
- 数据质量指标
- 系统资源使用

### 告警规则

- 模型MAPE超过阈值
- API响应时间过长
- 数据漂移检测
- 系统资源不足

## 开发指南

### 添加新的数据源

1. 在 `config.yaml` 中添加数据源配置
2. 在 `step1_data_collector.py` 中实现数据采集逻辑
3. 更新数据库schema

### 添加新的模型

1. 在 `step4_model_trainer.py` 中实现模型训练逻辑
2. 在 `step5_model_evaluator.py` 中添加评估支持
3. 更新配置文件

### 自定义特征

1. 在 `step3_feature_engineer.py` 中添加特征工程逻辑
2. 更新特征选择策略
3. 测试特征有效性

## 部署指南

### Docker 部署

```bash
# 构建镜像
docker build -t power-market-ml .

# 运行容器
docker-compose up -d
```

### Kubernetes 部署

```bash
# 应用配置
kubectl apply -f k8s/

# 检查状态
kubectl get pods -l app=power-market-ml
```

### 生产环境配置

- 配置负载均衡器
- 设置数据库连接池
- 配置日志聚合
- 设置监控告警

## 故障排除

### 常见问题

1. **数据采集失败**
   - 检查API端点配置
   - 验证网络连接
   - 查看错误日志

2. **模型训练内存不足**
   - 减少批次大小
   - 使用数据采样
   - 增加系统内存

3. **预测服务响应慢**
   - 检查模型大小
   - 优化特征处理
   - 启用缓存

### 日志查看

```bash
# 查看应用日志
tail -f logs/power_market_ml.log

# 查看Docker日志
docker logs power-market-api

# 查看Kubernetes日志
kubectl logs -f deployment/power-market-ml
```

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 联系方式

- 作者：Gregor
- 邮箱：<EMAIL>
- 项目地址：https://github.com/your-username/ElectricalEnergyMarket

## 更新日志

### v0.1-MVP (2025-08-25)

- 初始版本发布
- 实现完整ML流水线
- 支持多模型训练和评估
- 提供REST API服务
- 集成监控和告警系统
