"""
电力现货市场预测 ML 流水线 - 模型部署模块
作者：Gregor
版本：v0.1-MVP
日期：2025-08-25

功能：
1. 模型服务化部署
2. FastAPI REST API服务
3. 实时预测接口
4. 模型版本管理
5. 性能监控和日志
"""

import asyncio
import logging
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np
import joblib
import yaml
from pathlib import Path
from fastapi import FastAPI, HTTPException, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
import uvicorn
import redis
from prometheus_client import Counter, Histogram, Gauge, generate_latest
from prometheus_client.exposition import make_wsgi_app
import json
import traceback

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# Pydantic模型定义
class PredictionRequest(BaseModel):
    """预测请求模型"""
    features: Dict[str, float] = Field(..., description="输入特征字典")
    model_name: Optional[str] = Field(None, description="指定模型名称，默认使用最佳模型")
    prediction_horizon: int = Field(1, description="预测时长（15分钟为单位）")
    include_uncertainty: bool = Field(False, description="是否包含不确定性估计")


class PredictionResponse(BaseModel):
    """预测响应模型"""
    prediction_id: str = Field(..., description="预测ID")
    timestamp: datetime = Field(..., description="预测时间戳")
    model_name: str = Field(..., description="使用的模型名称")
    predictions: List[float] = Field(..., description="预测值列表")
    uncertainty: Optional[List[float]] = Field(None, description="不确定性估计")
    confidence_interval: Optional[Dict[str, List[float]]] = Field(None, description="置信区间")
    processing_time_ms: float = Field(..., description="处理时间（毫秒）")


class ModelInfo(BaseModel):
    """模型信息模型"""
    model_name: str
    version: str
    accuracy_metrics: Dict[str, float]
    last_updated: datetime
    status: str


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str
    timestamp: datetime
    models_loaded: int
    cache_status: str
    uptime_seconds: float


class PowerMarketPredictionService:
    """电力市场预测服务"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """初始化预测服务"""
        self.config = self._load_config(config_path)
        self.model_data_path = Path(self.config['data']['storage']['model_data_path'])
        self.feature_store_path = Path(self.config['data']['storage']['feature_store_path'])
        
        # 初始化组件
        self.models = {}
        self.scalers = {}
        self.feature_names = []
        self.best_model_name = None
        self.start_time = time.time()
        
        # 初始化Redis缓存
        self.redis_client = self._init_redis()
        
        # 初始化监控指标
        self._init_metrics()
        
        # 加载模型和相关数据
        self._load_models()
        self._load_scalers()
        self._load_feature_info()
        
        logger.info("预测服务初始化完成")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _init_redis(self) -> Optional[redis.Redis]:
        """初始化Redis连接"""
        try:
            redis_config = self.config['deployment']['cache']
            client = redis.Redis(
                host=redis_config['redis_host'],
                port=redis_config['redis_port'],
                decode_responses=True
            )
            client.ping()
            logger.info("Redis连接成功")
            return client
        except Exception as e:
            logger.warning(f"Redis连接失败: {str(e)}")
            return None
    
    def _init_metrics(self):
        """初始化Prometheus监控指标"""
        self.prediction_counter = Counter('predictions_total', 'Total number of predictions', ['model_name', 'status'])
        self.prediction_latency = Histogram('prediction_duration_seconds', 'Prediction processing time')
        self.model_accuracy = Gauge('model_accuracy', 'Model accuracy metrics', ['model_name', 'metric'])
        self.active_models = Gauge('active_models_count', 'Number of active models')
        self.cache_hits = Counter('cache_hits_total', 'Cache hit count')
        self.cache_misses = Counter('cache_misses_total', 'Cache miss count')
    
    def _load_models(self):
        """加载训练好的模型"""
        model_files = list(self.model_data_path.glob("*_model.joblib"))
        
        for model_file in model_files:
            model_name = model_file.stem.replace('_model', '')
            try:
                model = joblib.load(model_file)
                self.models[model_name] = model
                logger.info(f"加载模型: {model_name}")
            except Exception as e:
                logger.error(f"加载模型 {model_name} 失败: {str(e)}")
        
        # 加载评估结果确定最佳模型
        try:
            eval_results_path = self.model_data_path / "evaluation_results.joblib"
            if eval_results_path.exists():
                eval_results = joblib.load(eval_results_path)
                if 'validation' in eval_results:
                    best_mape = float('inf')
                    for model_name, metrics in eval_results['validation'].items():
                        if metrics.get('mape', float('inf')) < best_mape:
                            best_mape = metrics['mape']
                            self.best_model_name = model_name
                    logger.info(f"最佳模型: {self.best_model_name}")
        except Exception as e:
            logger.error(f"加载评估结果失败: {str(e)}")
        
        # 如果没有找到最佳模型，使用第一个可用模型
        if not self.best_model_name and self.models:
            self.best_model_name = list(self.models.keys())[0]
        
        self.active_models.set(len(self.models))
    
    def _load_scalers(self):
        """加载数据标准化器"""
        try:
            scalers_path = self.feature_store_path.parent / "processed" / "scalers.joblib"
            if scalers_path.exists():
                self.scalers = joblib.load(scalers_path)
                logger.info("加载数据标准化器成功")
        except Exception as e:
            logger.error(f"加载数据标准化器失败: {str(e)}")
    
    def _load_feature_info(self):
        """加载特征信息"""
        try:
            features_path = self.feature_store_path / "selected_features.joblib"
            if features_path.exists():
                self.feature_names = joblib.load(features_path)
                logger.info(f"加载特征信息成功，共 {len(self.feature_names)} 个特征")
        except Exception as e:
            logger.error(f"加载特征信息失败: {str(e)}")
    
    def _preprocess_features(self, features: Dict[str, float]) -> np.ndarray:
        """预处理输入特征"""
        # 创建特征向量
        feature_vector = np.zeros(len(self.feature_names))
        
        for i, feature_name in enumerate(self.feature_names):
            if feature_name in features:
                feature_vector[i] = features[feature_name]
        
        # 应用标准化
        if self.scalers:
            # 这里简化处理，实际应用中需要根据特征类型选择对应的scaler
            for scaler_name, scaler in self.scalers.items():
                if hasattr(scaler, 'transform'):
                    try:
                        # 假设所有特征都用同一个scaler（简化）
                        feature_vector = scaler.transform(feature_vector.reshape(1, -1)).flatten()
                        break
                    except:
                        continue
        
        return feature_vector
    
    def _get_cache_key(self, features: Dict[str, float], model_name: str) -> str:
        """生成缓存键"""
        # 简化的缓存键生成
        feature_hash = hash(str(sorted(features.items())))
        return f"prediction:{model_name}:{feature_hash}"
    
    async def predict(self, request: PredictionRequest) -> PredictionResponse:
        """执行预测"""
        start_time = time.time()
        prediction_id = str(uuid.uuid4())
        
        try:
            # 确定使用的模型
            model_name = request.model_name or self.best_model_name
            if model_name not in self.models:
                raise HTTPException(status_code=400, detail=f"模型 {model_name} 不存在")
            
            # 检查缓存
            cache_key = self._get_cache_key(request.features, model_name)
            cached_result = None
            
            if self.redis_client:
                try:
                    cached_result = self.redis_client.get(cache_key)
                    if cached_result:
                        self.cache_hits.inc()
                        cached_data = json.loads(cached_result)
                        logger.info(f"缓存命中: {prediction_id}")
                        return PredictionResponse(**cached_data)
                except Exception as e:
                    logger.warning(f"缓存读取失败: {str(e)}")
            
            self.cache_misses.inc()
            
            # 预处理特征
            feature_vector = self._preprocess_features(request.features)
            
            # 执行预测
            model = self.models[model_name]
            
            if request.prediction_horizon == 1:
                # 单步预测
                prediction = model.predict(feature_vector.reshape(1, -1))[0]
                predictions = [float(prediction)]
            else:
                # 多步预测（简化实现）
                predictions = []
                current_features = feature_vector.copy()
                
                for _ in range(request.prediction_horizon):
                    pred = model.predict(current_features.reshape(1, -1))[0]
                    predictions.append(float(pred))
                    
                    # 更新特征（简化：假设预测值影响下一步的某些特征）
                    if len(current_features) > 0:
                        current_features[0] = pred  # 假设第一个特征是滞后价格
            
            # 计算不确定性（简化实现）
            uncertainty = None
            confidence_interval = None
            
            if request.include_uncertainty:
                # 简化的不确定性估计
                uncertainty = [0.05 * abs(p) for p in predictions]  # 假设5%的相对误差
                confidence_interval = {
                    "lower_95": [p - 1.96 * u for p, u in zip(predictions, uncertainty)],
                    "upper_95": [p + 1.96 * u for p, u in zip(predictions, uncertainty)]
                }
            
            processing_time = (time.time() - start_time) * 1000
            
            # 创建响应
            response = PredictionResponse(
                prediction_id=prediction_id,
                timestamp=datetime.now(),
                model_name=model_name,
                predictions=predictions,
                uncertainty=uncertainty,
                confidence_interval=confidence_interval,
                processing_time_ms=processing_time
            )
            
            # 缓存结果
            if self.redis_client:
                try:
                    cache_data = response.dict()
                    cache_data['timestamp'] = cache_data['timestamp'].isoformat()
                    self.redis_client.setex(
                        cache_key, 
                        self.config['deployment']['cache']['cache_ttl'],
                        json.dumps(cache_data, default=str)
                    )
                except Exception as e:
                    logger.warning(f"缓存写入失败: {str(e)}")
            
            # 更新监控指标
            self.prediction_counter.labels(model_name=model_name, status='success').inc()
            self.prediction_latency.observe(processing_time / 1000)
            
            logger.info(f"预测完成: {prediction_id}, 模型: {model_name}, 耗时: {processing_time:.2f}ms")
            return response
            
        except Exception as e:
            self.prediction_counter.labels(model_name=model_name, status='error').inc()
            logger.error(f"预测失败: {prediction_id}, 错误: {str(e)}")
            raise HTTPException(status_code=500, detail=f"预测失败: {str(e)}")
    
    def get_model_info(self) -> List[ModelInfo]:
        """获取模型信息"""
        model_infos = []
        
        for model_name in self.models.keys():
            # 简化的模型信息
            model_info = ModelInfo(
                model_name=model_name,
                version="1.0.0",
                accuracy_metrics={"mape": 0.05, "rmse": 10.0},  # 示例数据
                last_updated=datetime.now() - timedelta(days=1),
                status="active"
            )
            model_infos.append(model_info)
        
        return model_infos
    
    def get_health_status(self) -> HealthResponse:
        """获取服务健康状态"""
        cache_status = "connected" if self.redis_client else "disconnected"
        
        return HealthResponse(
            status="healthy" if self.models else "unhealthy",
            timestamp=datetime.now(),
            models_loaded=len(self.models),
            cache_status=cache_status,
            uptime_seconds=time.time() - self.start_time
        )


# 创建FastAPI应用
app = FastAPI(
    title="电力现货市场预测API",
    description="基于机器学习的电力现货市场价格预测服务",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化预测服务
prediction_service = PowerMarketPredictionService()


@app.post("/predict", response_model=PredictionResponse)
async def predict_price(request: PredictionRequest):
    """预测电力价格"""
    return await prediction_service.predict(request)


@app.get("/models", response_model=List[ModelInfo])
async def get_models():
    """获取可用模型列表"""
    return prediction_service.get_model_info()


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查"""
    return prediction_service.get_health_status()


@app.get("/metrics")
async def get_metrics():
    """获取Prometheus监控指标"""
    return JSONResponse(
        content=generate_latest().decode('utf-8'),
        media_type="text/plain"
    )


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "电力现货市场预测API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health",
        "models": "/models"
    }


class ModelDeployer:
    """模型部署器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """初始化部署器"""
        self.config = self._load_config(config_path)
        logger.info("模型部署器初始化完成")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def deploy_api_service(self):
        """部署API服务"""
        logger.info("启动API服务")
        
        api_config = self.config['deployment']['api']
        
        uvicorn.run(
            "step6_model_deployer:app",
            host=api_config['host'],
            port=api_config['port'],
            workers=api_config['workers'],
            reload=False,
            access_log=True
        )
    
    def create_docker_config(self):
        """创建Docker配置文件"""
        dockerfile_content = """
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "step6_model_deployer.py"]
"""
        
        docker_compose_content = """
version: '3.8'

services:
  power-market-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - REDIS_HOST=redis
    depends_on:
      - redis
    restart: unless-stopped
  
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    restart: unless-stopped
  
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    restart: unless-stopped
  
  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    restart: unless-stopped
"""
        
        prometheus_config = """
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'power-market-api'
    static_configs:
      - targets: ['power-market-api:8000']
    metrics_path: '/metrics'
"""
        
        # 保存配置文件
        with open('Dockerfile', 'w') as f:
            f.write(dockerfile_content)
        
        with open('docker-compose.yml', 'w') as f:
            f.write(docker_compose_content)
        
        with open('prometheus.yml', 'w') as f:
            f.write(prometheus_config)
        
        logger.info("Docker配置文件已创建")
    
    def run_deployment_pipeline(self):
        """运行部署流水线"""
        logger.info("开始运行模型部署流水线")
        
        try:
            # 1. 创建Docker配置
            self.create_docker_config()
            
            # 2. 启动API服务
            self.deploy_api_service()
            
        except Exception as e:
            logger.error(f"部署流水线执行失败: {str(e)}")
            raise


def main():
    """主函数"""
    deployer = ModelDeployer()
    
    try:
        # 运行部署流水线
        deployer.run_deployment_pipeline()
        
    except KeyboardInterrupt:
        logger.info("服务已停止")
    except Exception as e:
        logger.error(f"部署失败: {str(e)}")


if __name__ == "__main__":
    main()
