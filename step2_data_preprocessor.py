"""
电力现货市场预测 ML 流水线 - 数据预处理模块
作者：Gregor
版本：v0.1-MVP
日期：2025-08-25

功能：
1. 数据清洗与异常检测
2. 缺失值处理
3. 数据标准化
4. 时序数据分割
5. 样本平衡处理
"""

import pandas as pd
import numpy as np
import sqlite3
import yaml
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import MinMaxScaler, StandardScaler, RobustScaler, QuantileTransformer
from sklearn.impute import KNNImputer
from sklearn.model_selection import TimeSeriesSplit
from imblearn.over_sampling import SMOTENC
from prophet import Prophet
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PowerMarketDataPreprocessor:
    """电力市场数据预处理器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """初始化预处理器"""
        self.config = self._load_config(config_path)
        self.raw_data_path = Path(self.config['data']['storage']['raw_data_path'])
        self.processed_data_path = Path(self.config['data']['storage']['processed_data_path'])
        self.processed_data_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化各种处理器
        self.anomaly_detector = PowerMarketAnomalyDetector()
        self.missing_value_handler = MissingValueHandler()
        self.data_normalizer = DataNormalizer()
        self.sample_balancer = SampleBalancer()
        
        logger.info("数据预处理器初始化完成")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def load_raw_data(self) -> Dict[str, pd.DataFrame]:
        """从数据库加载原始数据"""
        db_path = self.raw_data_path / "power_market.db"
        
        if not db_path.exists():
            logger.error(f"数据库文件不存在: {db_path}")
            return {}
        
        conn = sqlite3.connect(db_path)
        
        try:
            # 加载各类数据
            data_tables = {
                'market_data': 'SELECT * FROM market_data ORDER BY timestamp',
                'weather_data': 'SELECT * FROM weather_data ORDER BY timestamp',
                'load_data': 'SELECT * FROM load_data ORDER BY timestamp',
                'generation_data': 'SELECT * FROM generation_data ORDER BY timestamp'
            }
            
            datasets = {}
            for table_name, query in data_tables.items():
                try:
                    df = pd.read_sql_query(query, conn, parse_dates=['timestamp'])
                    df.set_index('timestamp', inplace=True)
                    datasets[table_name] = df
                    logger.info(f"加载 {table_name}: {len(df)} 条记录")
                except Exception as e:
                    logger.error(f"加载 {table_name} 失败: {str(e)}")
            
            return datasets
            
        finally:
            conn.close()
    
    def merge_datasets(self, datasets: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """合并多个数据集"""
        if not datasets:
            logger.error("没有数据集可合并")
            return pd.DataFrame()
        
        # 以市场数据为基准进行合并
        base_df = datasets.get('market_data')
        if base_df is None or base_df.empty:
            logger.error("市场数据为空，无法进行合并")
            return pd.DataFrame()
        
        merged_df = base_df.copy()
        
        # 逐个合并其他数据集
        for table_name, df in datasets.items():
            if table_name != 'market_data' and not df.empty:
                # 使用外连接合并，保留所有时间点
                merged_df = pd.merge(
                    merged_df, df, 
                    left_index=True, right_index=True, 
                    how='outer', 
                    suffixes=('', f'_{table_name}')
                )
                logger.info(f"合并 {table_name}，合并后数据量: {len(merged_df)}")
        
        # 按时间排序
        merged_df.sort_index(inplace=True)
        
        logger.info(f"数据合并完成，最终数据量: {len(merged_df)}, 特征数: {len(merged_df.columns)}")
        return merged_df
    
    def detect_and_handle_anomalies(self, df: pd.DataFrame) -> pd.DataFrame:
        """检测和处理异常值"""
        logger.info("开始异常检测和处理")
        
        # 通用异常检测
        df_cleaned = self.anomaly_detector.detect_general_anomalies(df)
        
        # 电力市场特有异常检测
        df_cleaned = self.anomaly_detector.detect_price_spikes(df_cleaned)
        df_cleaned = self.anomaly_detector.detect_generation_anomalies(df_cleaned)
        df_cleaned = self.anomaly_detector.detect_weather_anomalies(df_cleaned)
        
        logger.info("异常检测和处理完成")
        return df_cleaned
    
    def handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        logger.info("开始处理缺失值")
        
        # 统计缺失值
        missing_stats = df.isnull().sum()
        missing_ratio = missing_stats / len(df)
        
        logger.info(f"缺失值统计:\n{missing_ratio[missing_ratio > 0].sort_values(ascending=False)}")
        
        # 处理缺失值
        df_filled = self.missing_value_handler.handle_missing_values(df)
        
        logger.info("缺失值处理完成")
        return df_filled
    
    def normalize_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict]:
        """数据标准化"""
        logger.info("开始数据标准化")
        
        df_normalized, scalers = self.data_normalizer.normalize_data(df)
        
        logger.info("数据标准化完成")
        return df_normalized, scalers
    
    def split_time_series(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """时序数据分割"""
        logger.info("开始时序数据分割")
        
        train_ratio = self.config['training']['train_ratio']
        val_ratio = self.config['training']['validation_ratio']
        
        n_samples = len(df)
        train_size = int(n_samples * train_ratio)
        val_size = int(n_samples * val_ratio)
        
        # 时序分割，避免数据泄露
        train_df = df.iloc[:train_size]
        val_df = df.iloc[train_size:train_size + val_size]
        test_df = df.iloc[train_size + val_size:]
        
        splits = {
            'train': train_df,
            'validation': val_df,
            'test': test_df
        }
        
        for split_name, split_df in splits.items():
            logger.info(f"{split_name} 数据: {len(split_df)} 条记录 "
                       f"({split_df.index.min()} 到 {split_df.index.max()})")
        
        return splits
    
    def balance_samples(self, train_df: pd.DataFrame, target_column: str = 'price') -> pd.DataFrame:
        """样本平衡处理"""
        logger.info("开始样本平衡处理")
        
        balanced_df = self.sample_balancer.balance_samples(train_df, target_column)
        
        logger.info(f"样本平衡完成，原始样本: {len(train_df)}, 平衡后样本: {len(balanced_df)}")
        return balanced_df
    
    def save_processed_data(self, data_splits: Dict[str, pd.DataFrame], scalers: Dict):
        """保存处理后的数据"""
        logger.info("保存处理后的数据")
        
        # 保存数据分割
        for split_name, df in data_splits.items():
            file_path = self.processed_data_path / f"{split_name}_data.parquet"
            df.to_parquet(file_path)
            logger.info(f"保存 {split_name} 数据到: {file_path}")
        
        # 保存标准化器
        import joblib
        scaler_path = self.processed_data_path / "scalers.joblib"
        joblib.dump(scalers, scaler_path)
        logger.info(f"保存标准化器到: {scaler_path}")
    
    def run_preprocessing_pipeline(self):
        """运行完整的预处理流水线"""
        logger.info("开始运行数据预处理流水线")
        
        try:
            # 1. 加载原始数据
            raw_datasets = self.load_raw_data()
            if not raw_datasets:
                logger.error("无法加载原始数据，预处理流水线终止")
                return
            
            # 2. 合并数据集
            merged_df = self.merge_datasets(raw_datasets)
            if merged_df.empty:
                logger.error("数据合并失败，预处理流水线终止")
                return
            
            # 3. 异常检测和处理
            cleaned_df = self.detect_and_handle_anomalies(merged_df)
            
            # 4. 处理缺失值
            filled_df = self.handle_missing_values(cleaned_df)
            
            # 5. 数据标准化
            normalized_df, scalers = self.normalize_data(filled_df)
            
            # 6. 时序数据分割
            data_splits = self.split_time_series(normalized_df)
            
            # 7. 样本平衡（仅对训练集）
            if 'train' in data_splits:
                data_splits['train'] = self.balance_samples(data_splits['train'])
            
            # 8. 保存处理后的数据
            self.save_processed_data(data_splits, scalers)
            
            logger.info("数据预处理流水线完成")
            
            # 返回处理结果摘要
            summary = {
                'total_samples': len(normalized_df),
                'features': len(normalized_df.columns),
                'train_samples': len(data_splits.get('train', [])),
                'val_samples': len(data_splits.get('validation', [])),
                'test_samples': len(data_splits.get('test', []))
            }
            
            logger.info(f"预处理摘要: {summary}")
            return summary
            
        except Exception as e:
            logger.error(f"预处理流水线执行失败: {str(e)}")
            raise


class PowerMarketAnomalyDetector:
    """电力市场异常检测器"""
    
    def detect_general_anomalies(self, df: pd.DataFrame) -> pd.DataFrame:
        """通用异常检测"""
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        
        if len(numeric_columns) == 0:
            return df
        
        # 使用 Isolation Forest 检测异常
        iso_forest = IsolationForest(contamination=0.1, random_state=42)
        
        # 只对数值列进行异常检测
        numeric_data = df[numeric_columns].fillna(0)
        anomaly_labels = iso_forest.fit_predict(numeric_data)
        
        # 标记异常点
        df = df.copy()
        df['is_anomaly'] = anomaly_labels == -1
        
        # 记录异常统计
        anomaly_count = (anomaly_labels == -1).sum()
        logger.info(f"通用异常检测: 发现 {anomaly_count} 个异常点 ({anomaly_count/len(df)*100:.2f}%)")
        
        return df
    
    def detect_price_spikes(self, df: pd.DataFrame) -> pd.DataFrame:
        """价格尖峰检测"""
        if 'price' not in df.columns:
            return df
        
        df = df.copy()
        price_series = df['price'].dropna()
        
        if len(price_series) == 0:
            return df
        
        # 计算价格的滚动统计
        rolling_mean = price_series.rolling(window=96, min_periods=1).mean()  # 24小时滚动均值
        rolling_std = price_series.rolling(window=96, min_periods=1).std()
        
        # 检测超过3倍标准差的价格异常
        threshold = rolling_mean + 3 * rolling_std
        price_spikes = price_series > threshold
        
        df['price_spike'] = False
        df.loc[price_spikes.index, 'price_spike'] = price_spikes
        
        spike_count = price_spikes.sum()
        logger.info(f"价格尖峰检测: 发现 {spike_count} 个价格尖峰")
        
        return df
    
    def detect_generation_anomalies(self, df: pd.DataFrame) -> pd.DataFrame:
        """发电异常检测"""
        generation_columns = [col for col in df.columns if 'generation' in col.lower()]
        
        if not generation_columns:
            return df
        
        df = df.copy()
        
        for col in generation_columns:
            if col in df.columns:
                series = df[col].dropna()
                if len(series) > 0:
                    # 检测发电量的突变
                    diff = series.diff().abs()
                    threshold = diff.quantile(0.95)  # 95分位数作为阈值
                    
                    anomalies = diff > threshold
                    df[f'{col}_anomaly'] = False
                    df.loc[anomalies.index, f'{col}_anomaly'] = anomalies
        
        return df
    
    def detect_weather_anomalies(self, df: pd.DataFrame) -> pd.DataFrame:
        """天气异常检测"""
        weather_columns = ['temperature', 'humidity', 'wind_speed', 'solar_irradiance']
        weather_columns = [col for col in weather_columns if col in df.columns]
        
        if not weather_columns:
            return df
        
        df = df.copy()
        
        for col in weather_columns:
            series = df[col].dropna()
            if len(series) > 0:
                # 使用IQR方法检测异常
                Q1 = series.quantile(0.25)
                Q3 = series.quantile(0.75)
                IQR = Q3 - Q1
                
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                anomalies = (series < lower_bound) | (series > upper_bound)
                df[f'{col}_weather_anomaly'] = False
                df.loc[anomalies.index, f'{col}_weather_anomaly'] = anomalies
        
        return df


class MissingValueHandler:
    """缺失值处理器"""
    
    def handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        df_filled = df.copy()
        
        # 分别处理不同类型的列
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        categorical_columns = df.select_dtypes(include=['object', 'category']).columns
        datetime_columns = df.select_dtypes(include=['datetime64']).columns
        
        # 处理数值型缺失值 - 使用KNN插补
        if len(numeric_columns) > 0:
            knn_imputer = KNNImputer(n_neighbors=5)
            df_filled[numeric_columns] = knn_imputer.fit_transform(df_filled[numeric_columns])
        
        # 处理分类型缺失值 - 使用众数填充
        for col in categorical_columns:
            mode_value = df_filled[col].mode()
            if len(mode_value) > 0:
                df_filled[col].fillna(mode_value[0], inplace=True)
        
        # 处理时序型缺失值 - 使用前向填充
        for col in datetime_columns:
            df_filled[col].fillna(method='ffill', inplace=True)
        
        return df_filled


class DataNormalizer:
    """数据标准化器"""
    
    def normalize_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict]:
        """数据标准化"""
        df_normalized = df.copy()
        scalers = {}
        
        # 价格数据使用MinMax归一化
        price_columns = [col for col in df.columns if 'price' in col.lower()]
        if price_columns:
            scaler = MinMaxScaler()
            df_normalized[price_columns] = scaler.fit_transform(df_normalized[price_columns])
            scalers['price'] = scaler
        
        # 负荷数据使用Z-score标准化
        load_columns = [col for col in df.columns if 'load' in col.lower()]
        if load_columns:
            scaler = StandardScaler()
            df_normalized[load_columns] = scaler.fit_transform(df_normalized[load_columns])
            scalers['load'] = scaler
        
        # 天气数据使用鲁棒标准化
        weather_columns = ['temperature', 'humidity', 'wind_speed', 'solar_irradiance']
        weather_columns = [col for col in weather_columns if col in df.columns]
        if weather_columns:
            scaler = RobustScaler()
            df_normalized[weather_columns] = scaler.fit_transform(df_normalized[weather_columns])
            scalers['weather'] = scaler
        
        # 其他数值列使用分位数标准化
        other_numeric_columns = df.select_dtypes(include=[np.number]).columns
        other_columns = [col for col in other_numeric_columns 
                        if col not in price_columns + load_columns + weather_columns]
        if other_columns:
            scaler = QuantileTransformer(output_distribution='normal')
            df_normalized[other_columns] = scaler.fit_transform(df_normalized[other_columns])
            scalers['other'] = scaler
        
        return df_normalized, scalers


class SampleBalancer:
    """样本平衡器"""
    
    def balance_samples(self, df: pd.DataFrame, target_column: str) -> pd.DataFrame:
        """样本平衡处理"""
        if target_column not in df.columns:
            logger.warning(f"目标列 {target_column} 不存在，跳过样本平衡")
            return df
        
        # 创建价格分类标签（用于SMOTE）
        price_series = df[target_column].dropna()
        if len(price_series) == 0:
            return df
        
        # 将价格分为高、中、低三类
        price_quantiles = price_series.quantile([0.33, 0.67])
        
        def categorize_price(price):
            if price <= price_quantiles.iloc[0]:
                return 0  # 低价
            elif price <= price_quantiles.iloc[1]:
                return 1  # 中价
            else:
                return 2  # 高价
        
        df_balanced = df.copy()
        df_balanced['price_category'] = df_balanced[target_column].apply(categorize_price)
        
        # 检查是否需要平衡
        category_counts = df_balanced['price_category'].value_counts()
        if category_counts.min() / category_counts.max() > 0.5:
            logger.info("样本已相对平衡，跳过SMOTE处理")
            return df_balanced.drop('price_category', axis=1)
        
        # 准备SMOTE数据
        numeric_columns = df_balanced.select_dtypes(include=[np.number]).columns
        numeric_columns = [col for col in numeric_columns if col != 'price_category']
        
        if len(numeric_columns) == 0:
            logger.warning("没有数值特征可用于SMOTE，跳过样本平衡")
            return df_balanced.drop('price_category', axis=1)
        
        try:
            # 应用SMOTE
            smote = SMOTENC(categorical_features=[], random_state=42)
            X_resampled, y_resampled = smote.fit_resample(
                df_balanced[numeric_columns], 
                df_balanced['price_category']
            )
            
            # 重建DataFrame
            df_resampled = pd.DataFrame(X_resampled, columns=numeric_columns)
            df_resampled['price_category'] = y_resampled
            
            # 恢复时间索引（简单重复）
            original_index = df_balanced.index
            new_index = np.tile(original_index, len(df_resampled) // len(original_index) + 1)[:len(df_resampled)]
            df_resampled.index = new_index
            
            logger.info(f"SMOTE平衡完成，样本数从 {len(df_balanced)} 增加到 {len(df_resampled)}")
            return df_resampled.drop('price_category', axis=1)
            
        except Exception as e:
            logger.error(f"SMOTE处理失败: {str(e)}")
            return df_balanced.drop('price_category', axis=1)


def main():
    """主函数"""
    preprocessor = PowerMarketDataPreprocessor()
    
    try:
        # 运行预处理流水线
        summary = preprocessor.run_preprocessing_pipeline()
        print(f"预处理完成，摘要: {summary}")
        
    except Exception as e:
        logger.error(f"预处理失败: {str(e)}")


if __name__ == "__main__":
    main()
