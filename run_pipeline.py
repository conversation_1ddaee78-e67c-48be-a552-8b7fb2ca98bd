#!/usr/bin/env python3
"""
电力现货市场预测 ML 流水线 - 启动脚本
作者：Gregor
版本：v0.1-MVP
日期：2025-08-25

功能：
1. 提供命令行界面
2. 支持不同运行模式
3. 参数配置和验证
4. 错误处理和日志
"""

import argparse
import asyncio
import logging
import sys
from pathlib import Path
import yaml

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def setup_logging(log_level: str = "INFO"):
    """设置日志级别"""
    level = getattr(logging, log_level.upper(), logging.INFO)
    logging.getLogger().setLevel(level)
    logger.info(f"日志级别设置为: {log_level}")


def validate_config(config_path: str) -> bool:
    """验证配置文件"""
    try:
        if not Path(config_path).exists():
            logger.error(f"配置文件不存在: {config_path}")
            return False
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 检查必需的配置项
        required_sections = ['data', 'models', 'training', 'deployment']
        for section in required_sections:
            if section not in config:
                logger.error(f"配置文件缺少必需的节: {section}")
                return False
        
        logger.info("配置文件验证通过")
        return True
        
    except Exception as e:
        logger.error(f"配置文件验证失败: {str(e)}")
        return False


async def run_full_pipeline(config_path: str, steps: list = None):
    """运行完整流水线"""
    logger.info("启动完整ML流水线")
    
    try:
        from step8_pipeline_controller import PowerMarketMLPipeline
        
        pipeline = PowerMarketMLPipeline(config_path)
        summary = await pipeline.run_pipeline(steps)
        
        print("\n" + "="*60)
        print("流水线执行完成")
        print("="*60)
        print(f"状态: {summary['status']}")
        print(f"总耗时: {summary['total_duration_seconds']:.2f} 秒")
        print(f"完成步骤: {summary['completed_steps']}")
        print(f"失败步骤: {summary['failed_steps']}")
        
        return summary['status'] == 'completed'
        
    except Exception as e:
        logger.error(f"流水线执行失败: {str(e)}")
        return False


def run_data_collection(config_path: str):
    """运行数据采集"""
    logger.info("启动数据采集")
    
    try:
        from step1_data_collector import DataCollector
        
        collector = DataCollector(config_path)
        asyncio.run(collector.run_collection_cycle())
        
        print("数据采集完成")
        return True
        
    except Exception as e:
        logger.error(f"数据采集失败: {str(e)}")
        return False


def run_training_only(config_path: str):
    """仅运行训练相关步骤"""
    logger.info("启动训练流水线")
    
    try:
        steps = ['data_preprocessing', 'feature_engineering', 'model_training', 'model_evaluation']
        return asyncio.run(run_full_pipeline(config_path, steps))
        
    except Exception as e:
        logger.error(f"训练流水线失败: {str(e)}")
        return False


def run_api_service(config_path: str):
    """启动API服务"""
    logger.info("启动API服务")
    
    try:
        from step6_model_deployer import ModelDeployer
        
        deployer = ModelDeployer(config_path)
        deployer.deploy_api_service()
        
    except KeyboardInterrupt:
        logger.info("API服务已停止")
    except Exception as e:
        logger.error(f"API服务启动失败: {str(e)}")
        return False


def run_monitoring(config_path: str):
    """启动监控服务"""
    logger.info("启动监控服务")
    
    try:
        from step7_model_monitor import PowerMarketModelMonitor
        
        monitor = PowerMarketModelMonitor(config_path)
        monitor.start_scheduled_monitoring()
        
    except KeyboardInterrupt:
        logger.info("监控服务已停止")
    except Exception as e:
        logger.error(f"监控服务启动失败: {str(e)}")
        return False


def run_test_suite(config_path: str):
    """运行测试套件"""
    logger.info("启动测试套件")
    
    try:
        from test_pipeline import PipelineTester
        
        tester = PipelineTester()
        results = tester.run_comprehensive_test()
        
        success = results.get('overall_success', False)
        print(f"\n测试结果: {'✅ 通过' if success else '❌ 失败'}")
        
        return success
        
    except Exception as e:
        logger.error(f"测试套件执行失败: {str(e)}")
        return False


def create_sample_config():
    """创建示例配置文件"""
    sample_config_path = "config_sample.yaml"
    
    if Path(sample_config_path).exists():
        print(f"示例配置文件已存在: {sample_config_path}")
        return
    
    # 复制现有配置文件作为示例
    try:
        import shutil
        shutil.copy("config.yaml", sample_config_path)
        print(f"示例配置文件已创建: {sample_config_path}")
        print("请根据实际情况修改配置文件中的API端点和参数")
    except Exception as e:
        logger.error(f"创建示例配置文件失败: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="电力现货市场预测 ML 流水线",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python run_pipeline.py --mode full                    # 运行完整流水线
  python run_pipeline.py --mode training               # 仅运行训练
  python run_pipeline.py --mode api                    # 启动API服务
  python run_pipeline.py --mode monitoring             # 启动监控
  python run_pipeline.py --mode test                   # 运行测试
  python run_pipeline.py --mode data-collection        # 数据采集
  python run_pipeline.py --create-config               # 创建示例配置
        """
    )
    
    parser.add_argument(
        '--mode', 
        choices=['full', 'training', 'api', 'monitoring', 'test', 'data-collection'],
        default='full',
        help='运行模式 (默认: full)'
    )
    
    parser.add_argument(
        '--config',
        default='config.yaml',
        help='配置文件路径 (默认: config.yaml)'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    
    parser.add_argument(
        '--steps',
        nargs='+',
        help='指定要运行的步骤 (仅在full模式下有效)'
    )
    
    parser.add_argument(
        '--create-config',
        action='store_true',
        help='创建示例配置文件'
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    setup_logging(args.log_level)
    
    # 创建示例配置文件
    if args.create_config:
        create_sample_config()
        return
    
    # 验证配置文件
    if not validate_config(args.config):
        sys.exit(1)
    
    print("="*60)
    print("电力现货市场预测 ML 流水线")
    print("="*60)
    print(f"运行模式: {args.mode}")
    print(f"配置文件: {args.config}")
    print(f"日志级别: {args.log_level}")
    print("="*60)
    
    # 根据模式执行相应操作
    success = False
    
    try:
        if args.mode == 'full':
            success = asyncio.run(run_full_pipeline(args.config, args.steps))
        
        elif args.mode == 'training':
            success = run_training_only(args.config)
        
        elif args.mode == 'api':
            run_api_service(args.config)
            success = True  # API服务启动成功
        
        elif args.mode == 'monitoring':
            run_monitoring(args.config)
            success = True  # 监控服务启动成功
        
        elif args.mode == 'test':
            success = run_test_suite(args.config)
        
        elif args.mode == 'data-collection':
            success = run_data_collection(args.config)
        
        else:
            logger.error(f"未知的运行模式: {args.mode}")
            sys.exit(1)
    
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(0)
    
    except Exception as e:
        logger.error(f"执行失败: {str(e)}")
        sys.exit(1)
    
    # 退出状态
    if success:
        print("\n✅ 操作完成")
        sys.exit(0)
    else:
        print("\n❌ 操作失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
