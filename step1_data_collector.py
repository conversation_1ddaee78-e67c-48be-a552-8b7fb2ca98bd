"""
电力现货市场预测 ML 流水线 - 数据采集模块
作者：Gregor
版本：v0.1-MVP
日期：2025-08-25

功能：
1. 多源数据采集（市场数据、天气数据、负荷数据、机组数据、跨区数据、宏观数据）
2. 实时数据流处理
3. 数据质量检查
4. 数据存储管理
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import pandas as pd
import numpy as np
import aiohttp
import yaml
from dataclasses import dataclass
from pathlib import Path
import json
import sqlite3
from concurrent.futures import ThreadPoolExecutor
import schedule

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class DataSource:
    """数据源配置"""
    name: str
    api_endpoint: str
    update_frequency: int  # 分钟
    fields: List[str]
    api_key: Optional[str] = None
    timeout: int = 30


class DataCollector:
    """数据采集器主类"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """初始化数据采集器"""
        self.config = self._load_config(config_path)
        self.data_sources = self._setup_data_sources()
        self.storage_path = Path(self.config['data']['storage']['raw_data_path'])
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库连接
        self.db_path = self.storage_path / "power_market.db"
        self._init_database()
        
        # 数据质量检查器
        self.quality_checker = DataQualityChecker()
        
        logger.info("数据采集器初始化完成")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _setup_data_sources(self) -> List[DataSource]:
        """设置数据源"""
        sources = []
        for source_name, source_config in self.config['data']['sources'].items():
            source = DataSource(
                name=source_name,
                api_endpoint=source_config['api_endpoint'],
                update_frequency=source_config['update_frequency'],
                fields=source_config['fields']
            )
            sources.append(source)
        return sources
    
    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建数据表
        tables = {
            'market_data': '''
                CREATE TABLE IF NOT EXISTS market_data (
                    timestamp DATETIME PRIMARY KEY,
                    price REAL,
                    volume REAL,
                    bid_ask_spread REAL,
                    order_flow_imbalance REAL,
                    market_depth REAL,
                    data_quality_score REAL
                )
            ''',
            'weather_data': '''
                CREATE TABLE IF NOT EXISTS weather_data (
                    timestamp DATETIME PRIMARY KEY,
                    temperature REAL,
                    humidity REAL,
                    wind_speed REAL,
                    solar_irradiance REAL,
                    extreme_weather_alert INTEGER,
                    data_quality_score REAL
                )
            ''',
            'load_data': '''
                CREATE TABLE IF NOT EXISTS load_data (
                    timestamp DATETIME PRIMARY KEY,
                    total_load REAL,
                    load_change_rate REAL,
                    industrial_load REAL,
                    residential_load REAL,
                    commercial_load REAL,
                    data_quality_score REAL
                )
            ''',
            'generation_data': '''
                CREATE TABLE IF NOT EXISTS generation_data (
                    timestamp DATETIME PRIMARY KEY,
                    total_generation REAL,
                    coal_generation REAL,
                    gas_generation REAL,
                    renewable_generation REAL,
                    nuclear_generation REAL,
                    fuel_cost REAL,
                    data_quality_score REAL
                )
            '''
        }
        
        for table_name, create_sql in tables.items():
            cursor.execute(create_sql)
        
        conn.commit()
        conn.close()
        logger.info("数据库初始化完成")
    
    async def collect_data_from_source(self, source: DataSource) -> Optional[Dict]:
        """从单个数据源采集数据"""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=source.timeout)) as session:
                headers = {}
                if source.api_key:
                    headers['Authorization'] = f'Bearer {source.api_key}'
                
                async with session.get(source.api_endpoint, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # 数据质量检查
                        quality_score = self.quality_checker.check_data_quality(data, source.fields)
                        data['data_quality_score'] = quality_score
                        data['timestamp'] = datetime.now()
                        data['source'] = source.name
                        
                        logger.info(f"成功采集 {source.name} 数据，质量评分: {quality_score:.2f}")
                        return data
                    else:
                        logger.error(f"采集 {source.name} 数据失败，状态码: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"采集 {source.name} 数据异常: {str(e)}")
            return None
    
    async def collect_all_data(self) -> Dict[str, Any]:
        """并行采集所有数据源的数据"""
        tasks = []
        for source in self.data_sources:
            task = asyncio.create_task(self.collect_data_from_source(source))
            tasks.append((source.name, task))
        
        results = {}
        for source_name, task in tasks:
            try:
                data = await task
                if data:
                    results[source_name] = data
            except Exception as e:
                logger.error(f"采集 {source_name} 数据任务失败: {str(e)}")
        
        return results
    
    def save_data_to_database(self, data: Dict[str, Any]):
        """保存数据到数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            for source_name, source_data in data.items():
                if source_name == 'market_data':
                    cursor.execute('''
                        INSERT OR REPLACE INTO market_data 
                        (timestamp, price, volume, bid_ask_spread, order_flow_imbalance, 
                         market_depth, data_quality_score)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        source_data['timestamp'],
                        source_data.get('price', 0),
                        source_data.get('volume', 0),
                        source_data.get('bid_ask_spread', 0),
                        source_data.get('order_flow_imbalance', 0),
                        source_data.get('market_depth', 0),
                        source_data.get('data_quality_score', 0)
                    ))
                
                elif source_name == 'weather_data':
                    cursor.execute('''
                        INSERT OR REPLACE INTO weather_data 
                        (timestamp, temperature, humidity, wind_speed, solar_irradiance, 
                         extreme_weather_alert, data_quality_score)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        source_data['timestamp'],
                        source_data.get('temperature', 0),
                        source_data.get('humidity', 0),
                        source_data.get('wind_speed', 0),
                        source_data.get('solar_irradiance', 0),
                        source_data.get('extreme_weather_alert', 0),
                        source_data.get('data_quality_score', 0)
                    ))
                
                # 类似地处理其他数据源...
            
            conn.commit()
            logger.info("数据保存到数据库成功")
            
        except Exception as e:
            logger.error(f"保存数据到数据库失败: {str(e)}")
            conn.rollback()
        finally:
            conn.close()
    
    def save_data_to_files(self, data: Dict[str, Any]):
        """保存数据到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for source_name, source_data in data.items():
            file_path = self.storage_path / f"{source_name}_{timestamp}.json"
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(source_data, f, ensure_ascii=False, indent=2, default=str)
                logger.info(f"数据保存到文件: {file_path}")
            except Exception as e:
                logger.error(f"保存数据到文件失败: {str(e)}")
    
    async def run_collection_cycle(self):
        """运行一次数据采集周期"""
        logger.info("开始数据采集周期")
        start_time = time.time()
        
        # 采集数据
        collected_data = await self.collect_all_data()
        
        if collected_data:
            # 保存到数据库
            self.save_data_to_database(collected_data)
            
            # 保存到文件（备份）
            self.save_data_to_files(collected_data)
            
            # 数据统计
            total_sources = len(self.data_sources)
            successful_sources = len(collected_data)
            success_rate = successful_sources / total_sources * 100
            
            elapsed_time = time.time() - start_time
            logger.info(f"数据采集周期完成，成功率: {success_rate:.1f}% ({successful_sources}/{total_sources})，耗时: {elapsed_time:.2f}秒")
        else:
            logger.warning("本次采集周期未获取到任何数据")
    
    def start_scheduled_collection(self):
        """启动定时数据采集"""
        frequency = self.config['data']['collection_frequency']
        
        # 使用 schedule 库进行定时任务
        schedule.every(frequency).minutes.do(lambda: asyncio.run(self.run_collection_cycle()))
        
        logger.info(f"启动定时数据采集，频率: 每{frequency}分钟")
        
        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次


class DataQualityChecker:
    """数据质量检查器"""
    
    def check_data_quality(self, data: Dict, expected_fields: List[str]) -> float:
        """检查数据质量并返回评分（0-1）"""
        if not data:
            return 0.0
        
        score = 1.0
        
        # 检查必需字段完整性
        missing_fields = set(expected_fields) - set(data.keys())
        if missing_fields:
            score -= len(missing_fields) / len(expected_fields) * 0.3
        
        # 检查数值字段的有效性
        for field in expected_fields:
            if field in data:
                value = data[field]
                if value is None:
                    score -= 0.1
                elif isinstance(value, (int, float)) and (np.isnan(value) or np.isinf(value)):
                    score -= 0.1
        
        # 检查时间戳的合理性
        if 'timestamp' in data:
            try:
                timestamp = pd.to_datetime(data['timestamp'])
                now = pd.Timestamp.now()
                if abs((timestamp - now).total_seconds()) > 3600:  # 超过1小时
                    score -= 0.2
            except:
                score -= 0.2
        
        return max(0.0, score)


def main():
    """主函数"""
    collector = DataCollector()
    
    try:
        # 运行一次测试采集
        asyncio.run(collector.run_collection_cycle())
        
        # 启动定时采集（注释掉以避免无限循环）
        # collector.start_scheduled_collection()
        
    except KeyboardInterrupt:
        logger.info("数据采集器已停止")
    except Exception as e:
        logger.error(f"数据采集器运行异常: {str(e)}")


if __name__ == "__main__":
    main()
