"""
电力现货市场预测 ML 流水线 - 主流水线控制器
作者：Gregor
版本：v0.1-MVP
日期：2025-08-25

功能：
1. 统一流水线控制
2. 步骤编排和调度
3. 错误处理和重试
4. 流水线状态管理
5. 日志和监控集成
"""

import asyncio
import logging
import time
import yaml
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
import traceback
import json

# 导入各个步骤的模块
from step1_data_collector import DataCollector
from step2_data_preprocessor import PowerMarketDataPreprocessor
from step3_feature_engineer import PowerMarketFeatureEngineer
from step4_model_trainer import PowerMarketModelTrainer
from step5_model_evaluator import PowerMarketModelEvaluator
from step6_model_deployer import ModelDeployer
from step7_model_monitor import PowerMarketModelMonitor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PipelineStatus(Enum):
    """流水线状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class StepStatus(Enum):
    """步骤状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


@dataclass
class PipelineStep:
    """流水线步骤定义"""
    name: str
    description: str
    function: Callable
    dependencies: List[str]
    retry_count: int = 3
    timeout_minutes: int = 60
    required: bool = True


@dataclass
class StepResult:
    """步骤执行结果"""
    step_name: str
    status: StepStatus
    start_time: datetime
    end_time: Optional[datetime]
    duration_seconds: float
    result_data: Optional[Any]
    error_message: Optional[str]
    retry_count: int


class PowerMarketMLPipeline:
    """电力现货市场ML流水线控制器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """初始化流水线控制器"""
        self.config = self._load_config(config_path)
        self.pipeline_id = f"pipeline_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 流水线状态
        self.status = PipelineStatus.PENDING
        self.start_time = None
        self.end_time = None
        self.step_results = {}
        
        # 创建结果目录
        self.results_path = Path("pipeline_results")
        self.results_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化各个组件
        self.data_collector = DataCollector(config_path)
        self.data_preprocessor = PowerMarketDataPreprocessor(config_path)
        self.feature_engineer = PowerMarketFeatureEngineer(config_path)
        self.model_trainer = PowerMarketModelTrainer(config_path)
        self.model_evaluator = PowerMarketModelEvaluator(config_path)
        self.model_deployer = ModelDeployer(config_path)
        self.model_monitor = PowerMarketModelMonitor(config_path)
        
        # 定义流水线步骤
        self.pipeline_steps = self._define_pipeline_steps()
        
        logger.info(f"流水线控制器初始化完成，流水线ID: {self.pipeline_id}")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _define_pipeline_steps(self) -> List[PipelineStep]:
        """定义流水线步骤"""
        steps = [
            PipelineStep(
                name="data_collection",
                description="数据采集",
                function=self._run_data_collection,
                dependencies=[],
                retry_count=3,
                timeout_minutes=30,
                required=True
            ),
            PipelineStep(
                name="data_preprocessing",
                description="数据预处理",
                function=self._run_data_preprocessing,
                dependencies=["data_collection"],
                retry_count=2,
                timeout_minutes=60,
                required=True
            ),
            PipelineStep(
                name="feature_engineering",
                description="特征工程",
                function=self._run_feature_engineering,
                dependencies=["data_preprocessing"],
                retry_count=2,
                timeout_minutes=90,
                required=True
            ),
            PipelineStep(
                name="model_training",
                description="模型训练",
                function=self._run_model_training,
                dependencies=["feature_engineering"],
                retry_count=1,
                timeout_minutes=180,
                required=True
            ),
            PipelineStep(
                name="model_evaluation",
                description="模型评估",
                function=self._run_model_evaluation,
                dependencies=["model_training"],
                retry_count=2,
                timeout_minutes=60,
                required=True
            ),
            PipelineStep(
                name="model_deployment",
                description="模型部署",
                function=self._run_model_deployment,
                dependencies=["model_evaluation"],
                retry_count=1,
                timeout_minutes=30,
                required=False  # 部署可选
            ),
            PipelineStep(
                name="monitoring_setup",
                description="监控设置",
                function=self._run_monitoring_setup,
                dependencies=["model_deployment"],
                retry_count=1,
                timeout_minutes=15,
                required=False  # 监控设置可选
            )
        ]
        
        return steps
    
    async def _run_data_collection(self) -> Dict:
        """运行数据采集步骤"""
        logger.info("执行数据采集步骤")
        await self.data_collector.run_collection_cycle()
        return {"status": "completed", "message": "数据采集完成"}
    
    async def _run_data_preprocessing(self) -> Dict:
        """运行数据预处理步骤"""
        logger.info("执行数据预处理步骤")
        summary = self.data_preprocessor.run_preprocessing_pipeline()
        return {"status": "completed", "summary": summary}
    
    async def _run_feature_engineering(self) -> Dict:
        """运行特征工程步骤"""
        logger.info("执行特征工程步骤")
        summary = self.feature_engineer.run_feature_engineering_pipeline()
        return {"status": "completed", "summary": summary}
    
    async def _run_model_training(self) -> Dict:
        """运行模型训练步骤"""
        logger.info("执行模型训练步骤")
        summary = self.model_trainer.run_training_pipeline()
        return {"status": "completed", "summary": summary}
    
    async def _run_model_evaluation(self) -> Dict:
        """运行模型评估步骤"""
        logger.info("执行模型评估步骤")
        summary = self.model_evaluator.run_evaluation_pipeline()
        return {"status": "completed", "summary": summary}
    
    async def _run_model_deployment(self) -> Dict:
        """运行模型部署步骤"""
        logger.info("执行模型部署步骤")
        # 注意：这里不实际启动服务，只是准备部署配置
        self.model_deployer.create_docker_config()
        return {"status": "completed", "message": "部署配置已准备"}
    
    async def _run_monitoring_setup(self) -> Dict:
        """运行监控设置步骤"""
        logger.info("执行监控设置步骤")
        self.model_monitor.run_monitoring_cycle()
        return {"status": "completed", "message": "监控设置完成"}
    
    def _check_dependencies(self, step: PipelineStep) -> bool:
        """检查步骤依赖是否满足"""
        for dependency in step.dependencies:
            if dependency not in self.step_results:
                return False
            if self.step_results[dependency].status != StepStatus.COMPLETED:
                return False
        return True
    
    async def _execute_step_with_retry(self, step: PipelineStep) -> StepResult:
        """执行步骤（带重试机制）"""
        start_time = datetime.now()
        retry_count = 0
        last_error = None
        
        while retry_count <= step.retry_count:
            try:
                logger.info(f"执行步骤: {step.name} (尝试 {retry_count + 1}/{step.retry_count + 1})")
                
                # 设置超时
                result_data = await asyncio.wait_for(
                    step.function(),
                    timeout=step.timeout_minutes * 60
                )
                
                # 执行成功
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                return StepResult(
                    step_name=step.name,
                    status=StepStatus.COMPLETED,
                    start_time=start_time,
                    end_time=end_time,
                    duration_seconds=duration,
                    result_data=result_data,
                    error_message=None,
                    retry_count=retry_count
                )
                
            except asyncio.TimeoutError:
                last_error = f"步骤超时 ({step.timeout_minutes} 分钟)"
                logger.error(f"步骤 {step.name} 超时")
                
            except Exception as e:
                last_error = str(e)
                logger.error(f"步骤 {step.name} 执行失败: {str(e)}")
                logger.debug(traceback.format_exc())
            
            retry_count += 1
            
            # 如果还有重试机会，等待一段时间
            if retry_count <= step.retry_count:
                wait_time = min(60 * retry_count, 300)  # 最多等待5分钟
                logger.info(f"等待 {wait_time} 秒后重试...")
                await asyncio.sleep(wait_time)
        
        # 所有重试都失败
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        return StepResult(
            step_name=step.name,
            status=StepStatus.FAILED,
            start_time=start_time,
            end_time=end_time,
            duration_seconds=duration,
            result_data=None,
            error_message=last_error,
            retry_count=retry_count - 1
        )
    
    async def run_pipeline(self, steps_to_run: Optional[List[str]] = None) -> Dict:
        """运行完整流水线"""
        logger.info(f"开始运行流水线: {self.pipeline_id}")
        
        self.status = PipelineStatus.RUNNING
        self.start_time = datetime.now()
        
        try:
            # 确定要运行的步骤
            if steps_to_run:
                steps_to_execute = [step for step in self.pipeline_steps if step.name in steps_to_run]
            else:
                steps_to_execute = self.pipeline_steps
            
            # 按依赖顺序执行步骤
            for step in steps_to_execute:
                # 检查依赖
                if not self._check_dependencies(step):
                    logger.warning(f"步骤 {step.name} 的依赖未满足，跳过执行")
                    self.step_results[step.name] = StepResult(
                        step_name=step.name,
                        status=StepStatus.SKIPPED,
                        start_time=datetime.now(),
                        end_time=datetime.now(),
                        duration_seconds=0,
                        result_data=None,
                        error_message="依赖未满足",
                        retry_count=0
                    )
                    continue
                
                # 执行步骤
                step_result = await self._execute_step_with_retry(step)
                self.step_results[step.name] = step_result
                
                # 如果是必需步骤且失败了，停止流水线
                if step.required and step_result.status == StepStatus.FAILED:
                    logger.error(f"必需步骤 {step.name} 失败，停止流水线")
                    self.status = PipelineStatus.FAILED
                    break
                
                logger.info(f"步骤 {step.name} 完成，状态: {step_result.status.value}")
            
            # 确定最终状态
            if self.status == PipelineStatus.RUNNING:
                failed_required_steps = [
                    result for result in self.step_results.values()
                    if result.status == StepStatus.FAILED and 
                    any(step.required for step in self.pipeline_steps if step.name == result.step_name)
                ]
                
                if failed_required_steps:
                    self.status = PipelineStatus.FAILED
                else:
                    self.status = PipelineStatus.COMPLETED
            
            self.end_time = datetime.now()
            
            # 生成流水线报告
            pipeline_summary = self._generate_pipeline_summary()
            
            # 保存流水线结果
            self._save_pipeline_results(pipeline_summary)
            
            logger.info(f"流水线完成，状态: {self.status.value}")
            return pipeline_summary
            
        except Exception as e:
            self.status = PipelineStatus.FAILED
            self.end_time = datetime.now()
            logger.error(f"流水线执行异常: {str(e)}")
            raise
    
    def _generate_pipeline_summary(self) -> Dict:
        """生成流水线摘要"""
        total_duration = 0
        if self.start_time and self.end_time:
            total_duration = (self.end_time - self.start_time).total_seconds()
        
        step_summaries = {}
        for step_name, result in self.step_results.items():
            step_summaries[step_name] = {
                'status': result.status.value,
                'duration_seconds': result.duration_seconds,
                'retry_count': result.retry_count,
                'error_message': result.error_message
            }
        
        summary = {
            'pipeline_id': self.pipeline_id,
            'status': self.status.value,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'total_duration_seconds': total_duration,
            'steps': step_summaries,
            'completed_steps': len([r for r in self.step_results.values() if r.status == StepStatus.COMPLETED]),
            'failed_steps': len([r for r in self.step_results.values() if r.status == StepStatus.FAILED]),
            'skipped_steps': len([r for r in self.step_results.values() if r.status == StepStatus.SKIPPED])
        }
        
        return summary
    
    def _save_pipeline_results(self, summary: Dict):
        """保存流水线结果"""
        # 保存摘要
        summary_path = self.results_path / f"{self.pipeline_id}_summary.json"
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, ensure_ascii=False, indent=2)
        
        # 保存详细结果
        detailed_results = {
            'pipeline_id': self.pipeline_id,
            'config': self.config,
            'summary': summary,
            'step_results': {
                name: {
                    'step_name': result.step_name,
                    'status': result.status.value,
                    'start_time': result.start_time.isoformat(),
                    'end_time': result.end_time.isoformat() if result.end_time else None,
                    'duration_seconds': result.duration_seconds,
                    'result_data': result.result_data,
                    'error_message': result.error_message,
                    'retry_count': result.retry_count
                }
                for name, result in self.step_results.items()
            }
        }
        
        detailed_path = self.results_path / f"{self.pipeline_id}_detailed.json"
        with open(detailed_path, 'w', encoding='utf-8') as f:
            json.dump(detailed_results, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"流水线结果已保存: {summary_path}, {detailed_path}")
    
    def get_pipeline_status(self) -> Dict:
        """获取流水线状态"""
        return {
            'pipeline_id': self.pipeline_id,
            'status': self.status.value,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'current_step': self._get_current_step(),
            'completed_steps': [name for name, result in self.step_results.items() 
                              if result.status == StepStatus.COMPLETED],
            'failed_steps': [name for name, result in self.step_results.items() 
                           if result.status == StepStatus.FAILED]
        }
    
    def _get_current_step(self) -> Optional[str]:
        """获取当前执行的步骤"""
        for step in self.pipeline_steps:
            if step.name not in self.step_results:
                return step.name
        return None


async def main():
    """主函数"""
    pipeline = PowerMarketMLPipeline()
    
    try:
        # 运行完整流水线
        summary = await pipeline.run_pipeline()
        
        print("\n" + "="*50)
        print("流水线执行摘要")
        print("="*50)
        print(f"流水线ID: {summary['pipeline_id']}")
        print(f"状态: {summary['status']}")
        print(f"总耗时: {summary['total_duration_seconds']:.2f} 秒")
        print(f"完成步骤: {summary['completed_steps']}")
        print(f"失败步骤: {summary['failed_steps']}")
        print(f"跳过步骤: {summary['skipped_steps']}")
        
        print("\n步骤详情:")
        for step_name, step_info in summary['steps'].items():
            print(f"  {step_name}: {step_info['status']} ({step_info['duration_seconds']:.2f}s)")
            if step_info['error_message']:
                print(f"    错误: {step_info['error_message']}")
        
    except Exception as e:
        logger.error(f"流水线执行失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
