"""
电力现货市场预测 ML 流水线 - 特征工程模块
作者：Gregor
版本：v0.1-MVP
日期：2025-08-25

功能：
1. 时间特征工程
2. 市场特征工程
3. 外部特征工程
4. 滞后特征和滚动窗口特征
5. 特征选择和交互特征生成
"""

import pandas as pd
import numpy as np
import yaml
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.preprocessing import PolynomialFeatures
from boruta import BorutaPy
from sklearn.ensemble import RandomForestRegressor
import shap
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PowerMarketFeatureEngineer:
    """电力市场特征工程器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """初始化特征工程器"""
        self.config = self._load_config(config_path)
        self.processed_data_path = Path(self.config['data']['storage']['processed_data_path'])
        self.feature_store_path = Path(self.config['data']['storage']['feature_store_path'])
        self.feature_store_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化各种特征工程器
        self.time_feature_engineer = TimeFeatureEngineer(self.config)
        self.market_feature_engineer = MarketFeatureEngineer(self.config)
        self.external_feature_engineer = ExternalFeatureEngineer(self.config)
        self.lag_feature_engineer = LagFeatureEngineer(self.config)
        self.feature_selector = FeatureSelector(self.config)
        
        logger.info("特征工程器初始化完成")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def load_processed_data(self) -> Dict[str, pd.DataFrame]:
        """加载预处理后的数据"""
        data_splits = {}
        
        for split_name in ['train', 'validation', 'test']:
            file_path = self.processed_data_path / f"{split_name}_data.parquet"
            if file_path.exists():
                df = pd.read_parquet(file_path)
                data_splits[split_name] = df
                logger.info(f"加载 {split_name} 数据: {len(df)} 条记录, {len(df.columns)} 个特征")
            else:
                logger.warning(f"文件不存在: {file_path}")
        
        return data_splits
    
    def create_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建时间特征"""
        logger.info("开始创建时间特征")
        df_with_time_features = self.time_feature_engineer.create_features(df)
        logger.info(f"时间特征创建完成，新增 {len(df_with_time_features.columns) - len(df.columns)} 个特征")
        return df_with_time_features
    
    def create_market_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建市场特征"""
        logger.info("开始创建市场特征")
        df_with_market_features = self.market_feature_engineer.create_features(df)
        logger.info(f"市场特征创建完成，新增 {len(df_with_market_features.columns) - len(df.columns)} 个特征")
        return df_with_market_features
    
    def create_external_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建外部特征"""
        logger.info("开始创建外部特征")
        df_with_external_features = self.external_feature_engineer.create_features(df)
        logger.info(f"外部特征创建完成，新增 {len(df_with_external_features.columns) - len(df.columns)} 个特征")
        return df_with_external_features
    
    def create_lag_and_rolling_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建滞后和滚动窗口特征"""
        logger.info("开始创建滞后和滚动窗口特征")
        df_with_lag_features = self.lag_feature_engineer.create_features(df)
        logger.info(f"滞后和滚动特征创建完成，新增 {len(df_with_lag_features.columns) - len(df.columns)} 个特征")
        return df_with_lag_features
    
    def select_features(self, train_df: pd.DataFrame, target_column: str = 'price') -> Tuple[List[str], Dict]:
        """特征选择"""
        logger.info("开始特征选择")
        
        if target_column not in train_df.columns:
            logger.error(f"目标列 {target_column} 不存在")
            return list(train_df.columns), {}
        
        selected_features, feature_importance = self.feature_selector.select_features(
            train_df, target_column
        )
        
        logger.info(f"特征选择完成，从 {len(train_df.columns)} 个特征中选择了 {len(selected_features)} 个")
        return selected_features, feature_importance
    
    def create_interaction_features(self, df: pd.DataFrame, selected_features: List[str]) -> pd.DataFrame:
        """创建交互特征"""
        logger.info("开始创建交互特征")
        
        # 选择重要特征进行交互
        important_features = selected_features[:20]  # 取前20个重要特征
        
        # 创建二次交互特征
        interaction_features = []
        
        # 天气-负荷交互
        weather_features = [f for f in important_features if any(w in f.lower() for w in ['temperature', 'humidity', 'wind', 'solar'])]
        load_features = [f for f in important_features if 'load' in f.lower()]
        
        for weather_f in weather_features:
            for load_f in load_features:
                if weather_f in df.columns and load_f in df.columns:
                    interaction_name = f"{weather_f}_x_{load_f}"
                    df[interaction_name] = df[weather_f] * df[load_f]
                    interaction_features.append(interaction_name)
        
        # 价格-供需比交互
        price_features = [f for f in important_features if 'price' in f.lower()]
        supply_demand_features = [f for f in important_features if any(s in f.lower() for s in ['generation', 'load', 'capacity'])]
        
        for price_f in price_features:
            for sd_f in supply_demand_features:
                if price_f in df.columns and sd_f in df.columns:
                    interaction_name = f"{price_f}_x_{sd_f}"
                    df[interaction_name] = df[price_f] * df[sd_f]
                    interaction_features.append(interaction_name)
        
        # 时间-市场状态交互
        time_features = [f for f in important_features if any(t in f.lower() for t in ['hour', 'day', 'month', 'season'])]
        market_features = [f for f in important_features if any(m in f.lower() for m in ['volume', 'spread', 'depth'])]
        
        for time_f in time_features:
            for market_f in market_features:
                if time_f in df.columns and market_f in df.columns:
                    interaction_name = f"{time_f}_x_{market_f}"
                    df[interaction_name] = df[time_f] * df[market_f]
                    interaction_features.append(interaction_name)
        
        logger.info(f"交互特征创建完成，新增 {len(interaction_features)} 个交互特征")
        return df
    
    def save_features(self, data_splits: Dict[str, pd.DataFrame], selected_features: List[str], feature_importance: Dict):
        """保存特征工程结果"""
        logger.info("保存特征工程结果")
        
        # 保存特征数据
        for split_name, df in data_splits.items():
            file_path = self.feature_store_path / f"{split_name}_features.parquet"
            df.to_parquet(file_path)
            logger.info(f"保存 {split_name} 特征数据到: {file_path}")
        
        # 保存特征列表
        import joblib
        features_path = self.feature_store_path / "selected_features.joblib"
        joblib.dump(selected_features, features_path)
        
        # 保存特征重要性
        importance_path = self.feature_store_path / "feature_importance.joblib"
        joblib.dump(feature_importance, importance_path)
        
        logger.info("特征工程结果保存完成")
    
    def run_feature_engineering_pipeline(self):
        """运行完整的特征工程流水线"""
        logger.info("开始运行特征工程流水线")
        
        try:
            # 1. 加载预处理后的数据
            data_splits = self.load_processed_data()
            if not data_splits:
                logger.error("无法加载预处理数据，特征工程流水线终止")
                return
            
            # 2. 对每个数据集进行特征工程
            engineered_data_splits = {}
            
            for split_name, df in data_splits.items():
                logger.info(f"处理 {split_name} 数据集")
                
                # 创建各类特征
                df_features = self.create_time_features(df)
                df_features = self.create_market_features(df_features)
                df_features = self.create_external_features(df_features)
                df_features = self.create_lag_and_rolling_features(df_features)
                
                engineered_data_splits[split_name] = df_features
            
            # 3. 基于训练集进行特征选择
            if 'train' in engineered_data_splits:
                selected_features, feature_importance = self.select_features(
                    engineered_data_splits['train']
                )
                
                # 4. 创建交互特征
                for split_name in engineered_data_splits:
                    engineered_data_splits[split_name] = self.create_interaction_features(
                        engineered_data_splits[split_name], selected_features
                    )
                
                # 5. 应用特征选择到所有数据集
                final_features = selected_features + [col for col in engineered_data_splits['train'].columns 
                                                    if '_x_' in col]  # 包含交互特征
                
                for split_name in engineered_data_splits:
                    available_features = [f for f in final_features if f in engineered_data_splits[split_name].columns]
                    engineered_data_splits[split_name] = engineered_data_splits[split_name][available_features]
            
            # 6. 保存特征工程结果
            self.save_features(engineered_data_splits, final_features, feature_importance)
            
            logger.info("特征工程流水线完成")
            
            # 返回结果摘要
            summary = {
                'total_features': len(final_features) if 'train' in engineered_data_splits else 0,
                'selected_features': len(selected_features) if 'train' in engineered_data_splits else 0,
                'interaction_features': len([f for f in final_features if '_x_' in f]) if 'train' in engineered_data_splits else 0
            }
            
            for split_name, df in engineered_data_splits.items():
                summary[f'{split_name}_samples'] = len(df)
            
            logger.info(f"特征工程摘要: {summary}")
            return summary
            
        except Exception as e:
            logger.error(f"特征工程流水线执行失败: {str(e)}")
            raise


class TimeFeatureEngineer:
    """时间特征工程器"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def create_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建时间特征"""
        df_time = df.copy()
        
        # 确保索引是datetime类型
        if not isinstance(df_time.index, pd.DatetimeIndex):
            try:
                df_time.index = pd.to_datetime(df_time.index)
            except:
                logger.warning("无法将索引转换为datetime类型，跳过时间特征创建")
                return df_time
        
        # 基础时间特征
        df_time['hour'] = df_time.index.hour
        df_time['day_of_week'] = df_time.index.dayofweek
        df_time['day_of_month'] = df_time.index.day
        df_time['month'] = df_time.index.month
        df_time['quarter'] = df_time.index.quarter
        df_time['year'] = df_time.index.year
        
        # 15分钟时间槽（一天96个槽）
        df_time['time_slot'] = df_time.index.hour * 4 + df_time.index.minute // 15
        
        # 周期性编码（sin/cos）
        if self.config['features']['time_features']['enable_cyclical_encoding']:
            # 小时周期
            df_time['hour_sin'] = np.sin(2 * np.pi * df_time['hour'] / 24)
            df_time['hour_cos'] = np.cos(2 * np.pi * df_time['hour'] / 24)
            
            # 星期周期
            df_time['day_of_week_sin'] = np.sin(2 * np.pi * df_time['day_of_week'] / 7)
            df_time['day_of_week_cos'] = np.cos(2 * np.pi * df_time['day_of_week'] / 7)
            
            # 月份周期
            df_time['month_sin'] = np.sin(2 * np.pi * df_time['month'] / 12)
            df_time['month_cos'] = np.cos(2 * np.pi * df_time['month'] / 12)
            
            # 时间槽周期
            df_time['time_slot_sin'] = np.sin(2 * np.pi * df_time['time_slot'] / 96)
            df_time['time_slot_cos'] = np.cos(2 * np.pi * df_time['time_slot'] / 96)
        
        # 节假日和特殊时段标识
        if self.config['features']['time_features']['include_holidays']:
            df_time['is_weekend'] = (df_time['day_of_week'] >= 5).astype(int)
            df_time['is_holiday'] = 0  # 需要根据实际节假日数据设置
        
        # 峰谷时段标识
        if self.config['features']['time_features']['include_peak_valley']:
            df_time['is_peak_hour'] = ((df_time['hour'] >= 8) & (df_time['hour'] <= 11) | 
                                     (df_time['hour'] >= 18) & (df_time['hour'] <= 21)).astype(int)
            df_time['is_valley_hour'] = ((df_time['hour'] >= 23) | (df_time['hour'] <= 6)).astype(int)
        
        # 季节标识
        df_time['season'] = ((df_time['month'] % 12 + 3) // 3).map({1: 'spring', 2: 'summer', 3: 'autumn', 4: 'winter'})
        
        # 时间衍生特征
        df_time['days_since_year_start'] = df_time.index.dayofyear
        df_time['weeks_since_year_start'] = df_time.index.isocalendar().week
        
        return df_time


class MarketFeatureEngineer:
    """市场特征工程器"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def create_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建市场特征"""
        df_market = df.copy()
        
        # 价格特征
        if 'price' in df_market.columns:
            # 价格变化率
            df_market['price_change'] = df_market['price'].pct_change()
            df_market['price_change_abs'] = df_market['price_change'].abs()
            
            # 价格波动率（滚动标准差）
            for window in [4, 8, 16, 32]:  # 1小时到8小时
                df_market[f'price_volatility_{window}'] = df_market['price'].rolling(window=window).std()
            
            # 价格趋势（EMA）
            df_market['price_ema_short'] = df_market['price'].ewm(span=12).mean()
            df_market['price_ema_long'] = df_market['price'].ewm(span=48).mean()
            df_market['price_ema_signal'] = df_market['price_ema_short'] - df_market['price_ema_long']
        
        # 成交量特征
        if 'volume' in df_market.columns:
            df_market['volume_change'] = df_market['volume'].pct_change()
            df_market['volume_ma'] = df_market['volume'].rolling(window=24).mean()
            df_market['volume_ratio'] = df_market['volume'] / df_market['volume_ma']
        
        # 买卖价差特征
        if 'bid_ask_spread' in df_market.columns:
            df_market['spread_ma'] = df_market['bid_ask_spread'].rolling(window=24).mean()
            df_market['spread_ratio'] = df_market['bid_ask_spread'] / df_market['spread_ma']
        
        # 供需特征
        if 'total_generation' in df_market.columns and 'total_load' in df_market.columns:
            df_market['supply_demand_ratio'] = df_market['total_generation'] / (df_market['total_load'] + 1e-6)
            df_market['supply_demand_imbalance'] = df_market['total_generation'] - df_market['total_load']
            df_market['reserve_margin'] = (df_market['total_generation'] - df_market['total_load']) / df_market['total_load']
        
        # 市场深度和流动性指标
        if 'market_depth' in df_market.columns:
            df_market['market_depth_ma'] = df_market['market_depth'].rolling(window=24).mean()
            df_market['market_depth_ratio'] = df_market['market_depth'] / df_market['market_depth_ma']
        
        return df_market


class ExternalFeatureEngineer:
    """外部特征工程器"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def create_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建外部特征"""
        df_external = df.copy()
        
        # 天气特征
        weather_columns = ['temperature', 'humidity', 'wind_speed', 'solar_irradiance']
        
        for col in weather_columns:
            if col in df_external.columns:
                # 天气变化率
                df_external[f'{col}_change'] = df_external[col].pct_change()
                
                # 天气滚动统计
                for window in [4, 8, 24]:
                    df_external[f'{col}_ma_{window}'] = df_external[col].rolling(window=window).mean()
                    df_external[f'{col}_std_{window}'] = df_external[col].rolling(window=window).std()
        
        # 温度对负荷的非线性影响
        if 'temperature' in df_external.columns:
            df_external['temperature_squared'] = df_external['temperature'] ** 2
            df_external['temperature_cubed'] = df_external['temperature'] ** 3
            
            # 冷却度日和加热度日
            df_external['cooling_degree_days'] = np.maximum(df_external['temperature'] - 18, 0)
            df_external['heating_degree_days'] = np.maximum(18 - df_external['temperature'], 0)
        
        # 可再生能源特征
        renewable_columns = [col for col in df_external.columns if 'renewable' in col.lower()]
        for col in renewable_columns:
            # 可再生能源变化率
            df_external[f'{col}_change'] = df_external[col].pct_change()
            
            # 可再生能源渗透率
            if 'total_generation' in df_external.columns:
                df_external[f'{col}_penetration'] = df_external[col] / (df_external['total_generation'] + 1e-6)
        
        # 燃料价格特征
        if 'fuel_cost' in df_external.columns:
            df_external['fuel_cost_change'] = df_external['fuel_cost'].pct_change()
            df_external['fuel_cost_ma'] = df_external['fuel_cost'].rolling(window=24).mean()
        
        return df_external


class LagFeatureEngineer:
    """滞后特征工程器"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def create_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """创建滞后和滚动窗口特征"""
        df_lag = df.copy()
        
        # 获取配置
        lag_config = self.config['features']['lag_features']
        rolling_config = self.config['features']['rolling_features']
        
        # 创建滞后特征
        for feature_type, lags in lag_config.items():
            if feature_type == 'price_lags' and 'price' in df_lag.columns:
                for lag in lags:
                    df_lag[f'price_lag_{lag}'] = df_lag['price'].shift(lag)
            
            elif feature_type == 'load_lags':
                load_columns = [col for col in df_lag.columns if 'load' in col.lower()]
                for col in load_columns:
                    for lag in lags:
                        df_lag[f'{col}_lag_{lag}'] = df_lag[col].shift(lag)
            
            elif feature_type == 'weather_lags':
                weather_columns = ['temperature', 'humidity', 'wind_speed', 'solar_irradiance']
                for col in weather_columns:
                    if col in df_lag.columns:
                        for lag in lags:
                            df_lag[f'{col}_lag_{lag}'] = df_lag[col].shift(lag)
        
        # 创建滚动窗口特征
        key_columns = ['price', 'volume', 'total_load', 'total_generation', 'temperature']
        key_columns = [col for col in key_columns if col in df_lag.columns]
        
        for col in key_columns:
            for window in rolling_config['windows']:
                for stat in rolling_config['statistics']:
                    if stat == 'mean':
                        df_lag[f'{col}_rolling_mean_{window}'] = df_lag[col].rolling(window=window).mean()
                    elif stat == 'std':
                        df_lag[f'{col}_rolling_std_{window}'] = df_lag[col].rolling(window=window).std()
                    elif stat == 'min':
                        df_lag[f'{col}_rolling_min_{window}'] = df_lag[col].rolling(window=window).min()
                    elif stat == 'max':
                        df_lag[f'{col}_rolling_max_{window}'] = df_lag[col].rolling(window=window).max()
                    elif stat == 'skew':
                        df_lag[f'{col}_rolling_skew_{window}'] = df_lag[col].rolling(window=window).skew()
                    elif stat == 'kurt':
                        df_lag[f'{col}_rolling_kurt_{window}'] = df_lag[col].rolling(window=window).kurt()
        
        return df_lag


class FeatureSelector:
    """特征选择器"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def select_features(self, df: pd.DataFrame, target_column: str) -> Tuple[List[str], Dict]:
        """特征选择"""
        if target_column not in df.columns:
            return list(df.columns), {}
        
        # 准备数据
        feature_columns = [col for col in df.columns if col != target_column]
        X = df[feature_columns].fillna(0)
        y = df[target_column].fillna(0)
        
        # 移除常数特征
        constant_features = X.columns[X.std() == 0].tolist()
        X = X.drop(columns=constant_features)
        feature_columns = [col for col in feature_columns if col not in constant_features]
        
        if len(feature_columns) == 0:
            return [], {}
        
        # 使用Boruta进行特征选择
        method = self.config['features']['feature_selection']['method']
        max_features = self.config['features']['feature_selection']['max_features']
        
        if method == 'boruta':
            selected_features, importance = self._boruta_selection(X, y, max_features)
        elif method == 'shap':
            selected_features, importance = self._shap_selection(X, y, max_features)
        else:
            selected_features, importance = self._mutual_info_selection(X, y, max_features)
        
        return selected_features, importance
    
    def _boruta_selection(self, X: pd.DataFrame, y: pd.Series, max_features: int) -> Tuple[List[str], Dict]:
        """使用Boruta进行特征选择"""
        try:
            rf = RandomForestRegressor(n_estimators=100, random_state=42)
            boruta = BorutaPy(rf, n_estimators='auto', verbose=0, random_state=42, max_iter=50)
            
            boruta.fit(X.values, y.values)
            
            # 获取选中的特征
            selected_mask = boruta.support_
            selected_features = X.columns[selected_mask].tolist()
            
            # 限制特征数量
            if len(selected_features) > max_features:
                # 根据重要性排序
                feature_importance = dict(zip(X.columns, boruta.feature_importances_))
                selected_features = sorted(selected_features, 
                                         key=lambda x: feature_importance[x], 
                                         reverse=True)[:max_features]
            
            importance = dict(zip(X.columns, boruta.feature_importances_))
            
            return selected_features, importance
            
        except Exception as e:
            logger.error(f"Boruta特征选择失败: {str(e)}")
            return self._mutual_info_selection(X, y, max_features)
    
    def _shap_selection(self, X: pd.DataFrame, y: pd.Series, max_features: int) -> Tuple[List[str], Dict]:
        """使用SHAP进行特征选择"""
        try:
            # 训练模型
            rf = RandomForestRegressor(n_estimators=100, random_state=42)
            rf.fit(X, y)
            
            # 计算SHAP值
            explainer = shap.TreeExplainer(rf)
            shap_values = explainer.shap_values(X.sample(min(1000, len(X))))
            
            # 计算特征重要性
            feature_importance = np.abs(shap_values).mean(0)
            importance_dict = dict(zip(X.columns, feature_importance))
            
            # 选择最重要的特征
            selected_features = sorted(importance_dict.keys(), 
                                     key=lambda x: importance_dict[x], 
                                     reverse=True)[:max_features]
            
            return selected_features, importance_dict
            
        except Exception as e:
            logger.error(f"SHAP特征选择失败: {str(e)}")
            return self._mutual_info_selection(X, y, max_features)
    
    def _mutual_info_selection(self, X: pd.DataFrame, y: pd.Series, max_features: int) -> Tuple[List[str], Dict]:
        """使用互信息进行特征选择"""
        try:
            # 计算互信息
            mi_scores = mutual_info_regression(X, y, random_state=42)
            importance_dict = dict(zip(X.columns, mi_scores))
            
            # 选择最重要的特征
            selected_features = sorted(importance_dict.keys(), 
                                     key=lambda x: importance_dict[x], 
                                     reverse=True)[:max_features]
            
            return selected_features, importance_dict
            
        except Exception as e:
            logger.error(f"互信息特征选择失败: {str(e)}")
            # 返回所有特征
            return list(X.columns)[:max_features], {}


def main():
    """主函数"""
    feature_engineer = PowerMarketFeatureEngineer()
    
    try:
        # 运行特征工程流水线
        summary = feature_engineer.run_feature_engineering_pipeline()
        print(f"特征工程完成，摘要: {summary}")
        
    except Exception as e:
        logger.error(f"特征工程失败: {str(e)}")


if __name__ == "__main__":
    main()
