"""
电力现货市场预测 ML 流水线 - 模型监控模块
作者：Gregor
版本：v0.1-MVP
日期：2025-08-25

功能：
1. 模型性能监控
2. 数据漂移检测
3. 模型漂移检测
4. 自动告警系统
5. 监控报告生成
"""

import pandas as pd
import numpy as np
import yaml
import logging
import time
import smtplib
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
import joblib
import sqlite3
from scipy import stats
from sklearn.metrics import mean_absolute_percentage_error, mean_squared_error
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import schedule
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PowerMarketModelMonitor:
    """电力市场模型监控器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """初始化模型监控器"""
        self.config = self._load_config(config_path)
        self.model_data_path = Path(self.config['data']['storage']['model_data_path'])
        self.raw_data_path = Path(self.config['data']['storage']['raw_data_path'])
        
        # 创建监控结果目录
        self.monitoring_path = Path("monitoring_results")
        self.monitoring_path.mkdir(parents=True, exist_ok=True)
        
        # 初始化监控组件
        self.performance_monitor = PerformanceMonitor(self.config)
        self.drift_detector = DriftDetector(self.config)
        self.alert_system = AlertSystem(self.config)
        self.report_generator = MonitoringReportGenerator(self.config)
        
        # 监控状态
        self.monitoring_history = []
        self.last_monitoring_time = None
        
        logger.info("模型监控器初始化完成")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def load_recent_data(self, hours: int = 24) -> pd.DataFrame:
        """加载最近的数据"""
        try:
            db_path = self.raw_data_path / "power_market.db"
            if not db_path.exists():
                logger.error(f"数据库文件不存在: {db_path}")
                return pd.DataFrame()
            
            conn = sqlite3.connect(db_path)
            
            # 计算时间范围
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours)
            
            # 查询最近数据
            query = """
            SELECT m.timestamp, m.price, m.volume, w.temperature, l.total_load
            FROM market_data m
            LEFT JOIN weather_data w ON m.timestamp = w.timestamp
            LEFT JOIN load_data l ON m.timestamp = l.timestamp
            WHERE m.timestamp >= ? AND m.timestamp <= ?
            ORDER BY m.timestamp
            """
            
            df = pd.read_sql_query(
                query, conn, 
                params=[start_time.isoformat(), end_time.isoformat()],
                parse_dates=['timestamp']
            )
            
            conn.close()
            
            if not df.empty:
                df.set_index('timestamp', inplace=True)
                logger.info(f"加载最近 {hours} 小时数据: {len(df)} 条记录")
            else:
                logger.warning(f"未找到最近 {hours} 小时的数据")
            
            return df
            
        except Exception as e:
            logger.error(f"加载最近数据失败: {str(e)}")
            return pd.DataFrame()
    
    def load_model_predictions(self, hours: int = 24) -> Dict[str, np.ndarray]:
        """加载模型预测结果（模拟）"""
        # 在实际应用中，这里应该从预测日志或数据库中加载
        # 这里使用模拟数据
        recent_data = self.load_recent_data(hours)
        
        if recent_data.empty:
            return {}
        
        predictions = {}
        
        # 模拟不同模型的预测结果
        model_names = ['xgboost', 'lightgbm', 'tabnet']
        
        for model_name in model_names:
            # 添加一些随机噪声来模拟预测误差
            noise_factor = np.random.normal(1, 0.05, len(recent_data))
            predictions[model_name] = recent_data['price'].values * noise_factor
        
        return predictions
    
    def run_performance_monitoring(self, recent_data: pd.DataFrame, predictions: Dict[str, np.ndarray]) -> Dict:
        """运行性能监控"""
        logger.info("开始性能监控")
        
        if recent_data.empty or not predictions:
            logger.warning("没有足够的数据进行性能监控")
            return {}
        
        performance_results = self.performance_monitor.monitor_performance(
            recent_data, predictions
        )
        
        logger.info("性能监控完成")
        return performance_results
    
    def run_drift_detection(self, recent_data: pd.DataFrame) -> Dict:
        """运行漂移检测"""
        logger.info("开始漂移检测")
        
        if recent_data.empty:
            logger.warning("没有足够的数据进行漂移检测")
            return {}
        
        drift_results = self.drift_detector.detect_drift(recent_data)
        
        logger.info("漂移检测完成")
        return drift_results
    
    def check_alert_conditions(self, performance_results: Dict, drift_results: Dict) -> List[Dict]:
        """检查告警条件"""
        alerts = []
        
        # 性能告警
        if performance_results:
            for model_name, metrics in performance_results.items():
                if isinstance(metrics, dict):
                    mape = metrics.get('mape', 0)
                    if mape > self.config['mlops']['drift_detection']['performance_drift_threshold']:
                        alerts.append({
                            'type': 'performance',
                            'severity': 'high',
                            'model': model_name,
                            'message': f'模型 {model_name} MAPE ({mape:.4f}) 超过阈值',
                            'timestamp': datetime.now()
                        })
        
        # 漂移告警
        if drift_results:
            for drift_type, result in drift_results.items():
                if isinstance(result, dict) and result.get('drift_detected', False):
                    alerts.append({
                        'type': 'drift',
                        'severity': 'medium',
                        'drift_type': drift_type,
                        'message': f'检测到 {drift_type} 漂移',
                        'timestamp': datetime.now()
                    })
        
        return alerts
    
    def generate_monitoring_report(self, performance_results: Dict, drift_results: Dict, alerts: List[Dict]):
        """生成监控报告"""
        logger.info("生成监控报告")
        
        report = self.report_generator.generate_report(
            performance_results, drift_results, alerts
        )
        
        # 保存报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = self.monitoring_path / f"monitoring_report_{timestamp}.html"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"监控报告已保存: {report_path}")
    
    def run_monitoring_cycle(self):
        """运行一次监控周期"""
        logger.info("开始监控周期")
        start_time = time.time()
        
        try:
            # 1. 加载最近数据
            recent_data = self.load_recent_data(24)  # 最近24小时
            predictions = self.load_model_predictions(24)
            
            # 2. 性能监控
            performance_results = self.run_performance_monitoring(recent_data, predictions)
            
            # 3. 漂移检测
            drift_results = self.run_drift_detection(recent_data)
            
            # 4. 检查告警条件
            alerts = self.check_alert_conditions(performance_results, drift_results)
            
            # 5. 发送告警
            if alerts:
                self.alert_system.send_alerts(alerts)
            
            # 6. 生成监控报告
            self.generate_monitoring_report(performance_results, drift_results, alerts)
            
            # 7. 记录监控历史
            monitoring_record = {
                'timestamp': datetime.now(),
                'performance_results': performance_results,
                'drift_results': drift_results,
                'alerts_count': len(alerts),
                'processing_time': time.time() - start_time
            }
            
            self.monitoring_history.append(monitoring_record)
            self.last_monitoring_time = datetime.now()
            
            logger.info(f"监控周期完成，耗时: {time.time() - start_time:.2f}秒，告警数: {len(alerts)}")
            
        except Exception as e:
            logger.error(f"监控周期执行失败: {str(e)}")
    
    def start_scheduled_monitoring(self):
        """启动定时监控"""
        check_frequency = self.config['mlops']['drift_detection']['check_frequency']
        
        # 使用 schedule 库进行定时任务
        schedule.every(check_frequency).minutes.do(self.run_monitoring_cycle)
        
        logger.info(f"启动定时监控，频率: 每{check_frequency}分钟")
        
        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
    
    def get_monitoring_status(self) -> Dict:
        """获取监控状态"""
        return {
            'last_monitoring_time': self.last_monitoring_time,
            'monitoring_history_count': len(self.monitoring_history),
            'recent_alerts': [record for record in self.monitoring_history[-10:] if record['alerts_count'] > 0]
        }


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def monitor_performance(self, recent_data: pd.DataFrame, predictions: Dict[str, np.ndarray]) -> Dict:
        """监控模型性能"""
        performance_results = {}
        
        if 'price' not in recent_data.columns:
            logger.warning("数据中缺少价格列，无法进行性能监控")
            return performance_results
        
        y_true = recent_data['price'].values
        
        for model_name, y_pred in predictions.items():
            if len(y_pred) != len(y_true):
                logger.warning(f"模型 {model_name} 预测长度不匹配")
                continue
            
            try:
                # 计算性能指标
                mape = mean_absolute_percentage_error(y_true, y_pred)
                rmse = np.sqrt(mean_squared_error(y_true, y_pred))
                
                # 方向准确率
                if len(y_true) > 1:
                    true_direction = np.diff(y_true) > 0
                    pred_direction = np.diff(y_pred) > 0
                    direction_accuracy = np.mean(true_direction == pred_direction)
                else:
                    direction_accuracy = 0.0
                
                performance_results[model_name] = {
                    'mape': mape,
                    'rmse': rmse,
                    'direction_accuracy': direction_accuracy,
                    'sample_count': len(y_true)
                }
                
                logger.info(f"模型 {model_name} 性能: MAPE={mape:.4f}, RMSE={rmse:.4f}")
                
            except Exception as e:
                logger.error(f"计算模型 {model_name} 性能失败: {str(e)}")
        
        return performance_results


class DriftDetector:
    """漂移检测器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.reference_data = self._load_reference_data()
    
    def _load_reference_data(self) -> Optional[pd.DataFrame]:
        """加载参考数据（训练数据的统计信息）"""
        try:
            # 这里应该加载训练时的数据统计信息
            # 简化实现：返回None，在实际检测中使用历史数据作为参考
            return None
        except Exception as e:
            logger.error(f"加载参考数据失败: {str(e)}")
            return None
    
    def detect_drift(self, recent_data: pd.DataFrame) -> Dict:
        """检测数据漂移"""
        drift_results = {}
        
        if recent_data.empty:
            return drift_results
        
        # 数据漂移检测
        drift_results['data_drift'] = self._detect_data_drift(recent_data)
        
        # 概念漂移检测
        drift_results['concept_drift'] = self._detect_concept_drift(recent_data)
        
        # 特征漂移检测
        drift_results['feature_drift'] = self._detect_feature_drift(recent_data)
        
        return drift_results
    
    def _detect_data_drift(self, recent_data: pd.DataFrame) -> Dict:
        """检测数据分布漂移"""
        try:
            # 简化的数据漂移检测：比较最近数据与历史数据的分布
            numeric_columns = recent_data.select_dtypes(include=[np.number]).columns
            
            drift_detected = False
            drift_scores = {}
            
            for col in numeric_columns:
                if col in recent_data.columns:
                    recent_values = recent_data[col].dropna()
                    
                    if len(recent_values) < 10:
                        continue
                    
                    # 使用滑动窗口比较
                    if len(recent_values) >= 20:
                        mid_point = len(recent_values) // 2
                        early_values = recent_values[:mid_point]
                        late_values = recent_values[mid_point:]
                        
                        # KS检验
                        ks_stat, p_value = stats.ks_2samp(early_values, late_values)
                        drift_scores[col] = {'ks_stat': ks_stat, 'p_value': p_value}
                        
                        # 如果p值小于0.05，认为存在漂移
                        if p_value < 0.05:
                            drift_detected = True
            
            return {
                'drift_detected': drift_detected,
                'drift_scores': drift_scores,
                'method': 'ks_test'
            }
            
        except Exception as e:
            logger.error(f"数据漂移检测失败: {str(e)}")
            return {'drift_detected': False, 'error': str(e)}
    
    def _detect_concept_drift(self, recent_data: pd.DataFrame) -> Dict:
        """检测概念漂移"""
        try:
            # 简化的概念漂移检测：检查目标变量的分布变化
            if 'price' not in recent_data.columns:
                return {'drift_detected': False, 'reason': 'no_target_variable'}
            
            price_values = recent_data['price'].dropna()
            
            if len(price_values) < 20:
                return {'drift_detected': False, 'reason': 'insufficient_data'}
            
            # 比较前半部分和后半部分的分布
            mid_point = len(price_values) // 2
            early_prices = price_values[:mid_point]
            late_prices = price_values[mid_point:]
            
            # 计算统计差异
            mean_diff = abs(late_prices.mean() - early_prices.mean()) / early_prices.std()
            std_diff = abs(late_prices.std() - early_prices.std()) / early_prices.std()
            
            # 如果均值或标准差变化超过阈值，认为存在概念漂移
            drift_threshold = 0.5  # 可配置
            drift_detected = mean_diff > drift_threshold or std_diff > drift_threshold
            
            return {
                'drift_detected': drift_detected,
                'mean_diff': mean_diff,
                'std_diff': std_diff,
                'threshold': drift_threshold
            }
            
        except Exception as e:
            logger.error(f"概念漂移检测失败: {str(e)}")
            return {'drift_detected': False, 'error': str(e)}
    
    def _detect_feature_drift(self, recent_data: pd.DataFrame) -> Dict:
        """检测特征漂移"""
        try:
            # 简化的特征漂移检测：检查特征相关性变化
            numeric_columns = recent_data.select_dtypes(include=[np.number]).columns
            
            if len(numeric_columns) < 2:
                return {'drift_detected': False, 'reason': 'insufficient_features'}
            
            # 计算相关性矩阵
            if len(recent_data) >= 20:
                mid_point = len(recent_data) // 2
                early_corr = recent_data[:mid_point][numeric_columns].corr()
                late_corr = recent_data[mid_point:][numeric_columns].corr()
                
                # 计算相关性矩阵的差异
                corr_diff = np.abs(late_corr - early_corr).mean().mean()
                
                # 如果相关性变化超过阈值，认为存在特征漂移
                drift_threshold = 0.1  # 可配置
                drift_detected = corr_diff > drift_threshold
                
                return {
                    'drift_detected': drift_detected,
                    'correlation_diff': corr_diff,
                    'threshold': drift_threshold
                }
            else:
                return {'drift_detected': False, 'reason': 'insufficient_data'}
            
        except Exception as e:
            logger.error(f"特征漂移检测失败: {str(e)}")
            return {'drift_detected': False, 'error': str(e)}


class AlertSystem:
    """告警系统"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def send_alerts(self, alerts: List[Dict]):
        """发送告警"""
        for alert in alerts:
            self._send_single_alert(alert)
    
    def _send_single_alert(self, alert: Dict):
        """发送单个告警"""
        try:
            # 记录告警到日志
            logger.warning(f"告警: {alert['message']}")
            
            # 发送邮件告警（如果配置了）
            self._send_email_alert(alert)
            
            # 发送到监控系统（如果配置了）
            self._send_to_monitoring_system(alert)
            
        except Exception as e:
            logger.error(f"发送告警失败: {str(e)}")
    
    def _send_email_alert(self, alert: Dict):
        """发送邮件告警"""
        # 简化实现：只记录日志
        logger.info(f"邮件告警: {alert['message']}")
    
    def _send_to_monitoring_system(self, alert: Dict):
        """发送到监控系统"""
        # 简化实现：只记录日志
        logger.info(f"监控系统告警: {alert['message']}")


class MonitoringReportGenerator:
    """监控报告生成器"""
    
    def __init__(self, config: Dict):
        self.config = config
    
    def generate_report(self, performance_results: Dict, drift_results: Dict, alerts: List[Dict]) -> str:
        """生成监控报告"""
        html_template = """
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>模型监控报告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
                .header {{ background-color: #f4f4f4; padding: 20px; border-radius: 5px; }}
                .section {{ margin: 20px 0; }}
                .alert {{ background-color: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; border-radius: 5px; margin: 5px 0; }}
                .success {{ background-color: #d4edda; border: 1px solid #c3e6cb; padding: 10px; border-radius: 5px; }}
                .metrics-table {{ width: 100%; border-collapse: collapse; margin: 15px 0; }}
                .metrics-table th, .metrics-table td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                .metrics-table th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>电力市场模型监控报告</h1>
                <p><strong>生成时间:</strong> {timestamp}</p>
                <p><strong>监控周期:</strong> 最近24小时</p>
            </div>
            
            <div class="section">
                <h2>1. 告警摘要</h2>
                {alerts_summary}
            </div>
            
            <div class="section">
                <h2>2. 模型性能监控</h2>
                {performance_summary}
            </div>
            
            <div class="section">
                <h2>3. 数据漂移检测</h2>
                {drift_summary}
            </div>
            
            <div class="section">
                <h2>4. 建议措施</h2>
                {recommendations}
            </div>
        </body>
        </html>
        """
        
        # 生成各部分内容
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        alerts_summary = self._generate_alerts_summary(alerts)
        performance_summary = self._generate_performance_summary(performance_results)
        drift_summary = self._generate_drift_summary(drift_results)
        recommendations = self._generate_recommendations(alerts, performance_results, drift_results)
        
        # 填充模板
        report = html_template.format(
            timestamp=timestamp,
            alerts_summary=alerts_summary,
            performance_summary=performance_summary,
            drift_summary=drift_summary,
            recommendations=recommendations
        )
        
        return report
    
    def _generate_alerts_summary(self, alerts: List[Dict]) -> str:
        """生成告警摘要"""
        if not alerts:
            return '<div class="success">✅ 无告警</div>'
        
        html = f'<div class="alert">⚠️ 共发现 {len(alerts)} 个告警</div>'
        
        for alert in alerts:
            html += f'''
            <div class="alert">
                <strong>{alert['type'].upper()}:</strong> {alert['message']}
                <br><small>时间: {alert['timestamp']}</small>
            </div>
            '''
        
        return html
    
    def _generate_performance_summary(self, performance_results: Dict) -> str:
        """生成性能摘要"""
        if not performance_results:
            return '<p>暂无性能数据</p>'
        
        html = '<table class="metrics-table">'
        html += '<tr><th>模型</th><th>MAPE</th><th>RMSE</th><th>方向准确率</th><th>样本数</th></tr>'
        
        for model_name, metrics in performance_results.items():
            if isinstance(metrics, dict):
                html += f'''
                <tr>
                    <td>{model_name}</td>
                    <td>{metrics.get('mape', 0):.4f}</td>
                    <td>{metrics.get('rmse', 0):.4f}</td>
                    <td>{metrics.get('direction_accuracy', 0):.4f}</td>
                    <td>{metrics.get('sample_count', 0)}</td>
                </tr>
                '''
        
        html += '</table>'
        return html
    
    def _generate_drift_summary(self, drift_results: Dict) -> str:
        """生成漂移摘要"""
        if not drift_results:
            return '<p>暂无漂移检测数据</p>'
        
        html = '<ul>'
        
        for drift_type, result in drift_results.items():
            if isinstance(result, dict):
                status = "检测到漂移" if result.get('drift_detected', False) else "无漂移"
                html += f'<li><strong>{drift_type}:</strong> {status}</li>'
        
        html += '</ul>'
        return html
    
    def _generate_recommendations(self, alerts: List[Dict], performance_results: Dict, drift_results: Dict) -> str:
        """生成建议措施"""
        recommendations = []
        
        if alerts:
            recommendations.append("• 立即检查告警原因并采取相应措施")
        
        # 基于性能结果的建议
        if performance_results:
            high_mape_models = [name for name, metrics in performance_results.items() 
                              if isinstance(metrics, dict) and metrics.get('mape', 0) > 0.1]
            if high_mape_models:
                recommendations.append(f"• 模型 {', '.join(high_mape_models)} 的MAPE较高，建议重新训练")
        
        # 基于漂移结果的建议
        if drift_results:
            drift_detected = any(result.get('drift_detected', False) for result in drift_results.values() 
                               if isinstance(result, dict))
            if drift_detected:
                recommendations.append("• 检测到数据漂移，建议更新训练数据并重新训练模型")
        
        if not recommendations:
            recommendations.append("• 系统运行正常，继续监控")
        
        html = '<ul>'
        for rec in recommendations:
            html += f'<li>{rec}</li>'
        html += '</ul>'
        
        return html


def main():
    """主函数"""
    monitor = PowerMarketModelMonitor()
    
    try:
        # 运行一次监控周期
        monitor.run_monitoring_cycle()
        
        # 启动定时监控（注释掉以避免无限循环）
        # monitor.start_scheduled_monitoring()
        
    except KeyboardInterrupt:
        logger.info("监控已停止")
    except Exception as e:
        logger.error(f"监控失败: {str(e)}")


if __name__ == "__main__":
    main()
