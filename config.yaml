# 电力现货市场预测 ML 流水线配置文件
# 作者：Gregor
# 版本：v0.1-MVP

# 数据配置
data:
  # 数据采集频率（分钟）
  collection_frequency: 15
  
  # 数据源配置
  sources:
    market_data:
      api_endpoint: "https://api.powermarket.com/v1"
      update_frequency: 15  # 分钟
      fields: ["price", "volume", "clearing_results", "bid_ask_spread", "order_flow_imbalance"]
    
    weather_data:
      api_endpoint: "https://api.weather.com/v1"
      update_frequency: 15
      fields: ["temperature", "humidity", "wind_speed", "solar_irradiance", "extreme_weather_alerts"]
    
    load_data:
      api_endpoint: "https://api.gridoperator.com/v1"
      update_frequency: 15
      fields: ["historical_load", "real_time_load", "load_curve", "load_change_rate"]
    
    generation_data:
      api_endpoint: "https://api.generation.com/v1"
      update_frequency: 15
      fields: ["generation_plan", "maintenance_schedule", "fuel_prices", "renewable_forecast"]
    
    cross_region_data:
      api_endpoint: "https://api.crossregion.com/v1"
      update_frequency: 15
      fields: ["adjacent_prices", "transmission_congestion", "inter_region_flow"]
    
    macro_data:
      api_endpoint: "https://api.economic.com/v1"
      update_frequency: 60  # 小时
      fields: ["economic_indicators", "policy_changes", "emergency_events"]

  # 数据存储配置
  storage:
    raw_data_path: "data/raw"
    processed_data_path: "data/processed"
    feature_store_path: "data/features"
    model_data_path: "data/model"
    
    # 数据库配置
    database:
      host: "localhost"
      port: 5432
      name: "power_market_db"
      user: "ml_user"
      password: "${DB_PASSWORD}"
    
    # 时序数据库配置
    timeseries_db:
      host: "localhost"
      port: 8086
      name: "power_market_ts"
      user: "ts_user"
      password: "${TSDB_PASSWORD}"

# 特征工程配置
features:
  # 时间特征
  time_features:
    enable_cyclical_encoding: true
    time_slots_per_day: 96  # 15分钟间隔
    include_holidays: true
    include_peak_valley: true
    
  # 滞后特征
  lag_features:
    price_lags: [1, 2, 4, 8, 16, 32, 96]  # 15分钟到24小时
    load_lags: [1, 2, 4, 8, 16, 32, 96]
    weather_lags: [1, 2, 4, 8]
    
  # 滚动窗口特征
  rolling_features:
    windows: [4, 8, 16, 32, 96]  # 1小时到24小时
    statistics: ["mean", "std", "min", "max", "skew", "kurt"]
    
  # 特征选择
  feature_selection:
    method: "boruta"  # boruta, shap, mrmr
    max_features: 200
    importance_threshold: 0.01

# 模型配置
models:
  # 短期预测模型（15分钟-24小时）
  short_term:
    primary_model: "enhanced_tft"
    auxiliary_models: ["xgboost", "tabnet", "lightgbm"]
    ensemble_method: "dynamic_weighted"
    
    enhanced_tft:
      hidden_size: 256
      attention_heads: 8
      num_layers: 3
      dropout: 0.2
      learning_rate: 0.001
      batch_size: 64
      max_epochs: 100
      
    xgboost:
      n_estimators: 1000
      max_depth: 8
      learning_rate: 0.1
      subsample: 0.8
      colsample_bytree: 0.8
      
    lightgbm:
      n_estimators: 1000
      max_depth: 8
      learning_rate: 0.1
      num_leaves: 31
      feature_fraction: 0.8
      
  # 中期预测模型（1-7天）
  medium_term:
    primary_model: "neural_prophet"
    auxiliary_models: ["var", "monte_carlo"]
    ensemble_method: "quantile_regression"
    
  # 长期预测模型（1月-1年）
  long_term:
    primary_model: "vecm_garch"
    auxiliary_models: ["prophet", "regression"]
    ensemble_method: "scenario_modeling"

# 训练配置
training:
  # 数据分割
  train_ratio: 0.8
  validation_ratio: 0.15
  test_ratio: 0.05
  
  # 滑动窗口训练
  sliding_window:
    window_size: 2880  # 30天 * 96个15分钟
    update_frequency: 15  # 分钟
    decay_factor: 0.995
    
  # 样本权重
  sample_weights:
    recent_weight: 1.2
    extreme_event_weight: 3.0
    seasonal_weights:
      summer: 1.2
      winter: 1.1
      spring: 1.0
      autumn: 1.0
      
  # 优化器配置
  optimizer:
    type: "adam"
    learning_rate: 0.001
    weight_decay: 1e-5
    
  # 早停配置
  early_stopping:
    patience: 10
    min_delta: 0.001
    monitor: "val_mape"

# 评估配置
evaluation:
  metrics:
    accuracy: ["mape", "rmse", "mae"]
    directional: ["direction_accuracy", "trend_consistency"]
    probabilistic: ["quantile_coverage", "pinball_loss"]
    robustness: ["extreme_event_mape", "stress_test_performance"]
    economic: ["prediction_value", "trading_profit", "sharpe_ratio"]
    realtime: ["prediction_latency", "system_response_time"]
    
  # 评估窗口
  evaluation_windows:
    short_term: [15, 60, 240, 1440]  # 15分钟到24小时
    medium_term: [1440, 10080]  # 1天到7天
    long_term: [43200, 525600]  # 1月到1年

# 部署配置
deployment:
  # API服务配置
  api:
    host: "0.0.0.0"
    port: 8000
    workers: 4
    timeout: 30
    
  # 缓存配置
  cache:
    redis_host: "localhost"
    redis_port: 6379
    cache_ttl: 900  # 15分钟
    
  # 消息队列配置
  message_queue:
    broker_url: "redis://localhost:6379/0"
    result_backend: "redis://localhost:6379/0"
    
  # 监控配置
  monitoring:
    prometheus_port: 9090
    grafana_port: 3000
    log_level: "INFO"
    
  # 性能目标
  performance_targets:
    p99_latency: 50  # ms
    p95_latency: 30  # ms
    max_qps: 1000
    availability: 0.999

# MLOps配置
mlops:
  # 实验跟踪
  experiment_tracking:
    backend: "mlflow"  # mlflow, wandb, neptune
    tracking_uri: "http://localhost:5000"
    
  # 模型注册
  model_registry:
    backend: "mlflow"
    registry_uri: "http://localhost:5000"
    
  # 模型漂移检测
  drift_detection:
    data_drift_threshold: 0.1
    concept_drift_threshold: 0.05
    performance_drift_threshold: 0.05
    check_frequency: 60  # 分钟
    
  # 自动重训练
  auto_retrain:
    trigger_conditions:
      mape_threshold: 0.05
      consecutive_hours_threshold: 3
      drift_score_threshold: 0.1
    retrain_frequency: "daily"
    
  # A/B测试
  ab_testing:
    traffic_split: 0.1  # 10%流量用于新模型
    significance_level: 0.05
    minimum_sample_size: 1000

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: "logs/power_market_ml.log"
  max_file_size: "100MB"
  backup_count: 5

# 安全配置
security:
  api_key_required: true
  rate_limiting:
    requests_per_minute: 100
    burst_size: 20
  data_encryption: true
  audit_logging: true
